I really love to have:
full email inteligence, we want system that fully drain data from emails + transcriptions, and with superpowerfull agentic crewai/langchain analisys provide relevand data about my clients. Because boss is aswering to many phonecalls. and he input not enaugh data to our system.
+ we had tons of porrly structurized calendar events, we want to have system that will help us to structure them and provide us with relevant data. 
/home/<USER>/HVAC/unifikacja/Data_to_ingest - dane archiwalne - prosze dokonać ingest do systemu.
/home/<USER>/HVAC/unifikacja/python_mixer
From just transcribtion that we can <NAME_EMAIL> i want to system to provide like new kanban point custiomized after analisys or even propozycja oferty - fully inteligent solution to nawadniać nasz sytem

I belive that weaviate is the best option for vector database it provide superpowerfull semantic search and filtering. 
take weaviate config from : [text](../../Gobeklitepe)
www.fulmark.pl

Main działalnosc is selling whole services around HVAC split air contidioners. installing service repair etc

przykładowa oferta to: 
propozycja 3 urządzeń, zazwyczaj LG zdjecia i opis
opis zakresu prac, cena
