#!/usr/bin/env node
/**
 * 🧪 Test Weaviate Setup and Integration
 * Sets up schemas and tests the Gobeklitepe integration
 */

const axios = require('axios');

const WEAVIATE_URL = 'http://localhost:8082';

async function setupWeaviateSchemas() {
  console.log('🔧 Setting up Weaviate schemas...');

  const schemas = [
    {
      class: 'HVACCustomer',
      description: 'Enhanced HVAC customer profiles with semantic intelligence',
      properties: [
        { name: 'customerId', dataType: ['string'], description: 'Unique customer identifier' },
        { name: 'companyName', dataType: ['string'], description: 'Company or customer name' },
        { name: 'contactInfo', dataType: ['object'], description: 'Contact information' },
        { name: 'businessType', dataType: ['string'], description: 'Type of business' },
        { name: 'buildingSize', dataType: ['number'], description: 'Building size in square meters' },
        { name: 'equipmentHistory', dataType: ['object'], description: 'HVAC equipment history' },
        { name: 'serviceHistory', dataType: ['object'], description: 'Service interaction history' },
        { name: 'communicationPreferences', dataType: ['object'], description: 'Communication preferences' },
        { name: 'aiInsights', dataType: ['object'], description: 'AI-generated customer insights' },
        { name: 'lastUpdated', dataType: ['date'], description: 'Last profile update timestamp' }
      ],
      vectorizer: 'text2vec-transformers'
    },
    {
      class: 'HVACInteraction',
      description: 'Customer interactions with semantic analysis',
      properties: [
        { name: 'interactionId', dataType: ['string'], description: 'Unique interaction identifier' },
        { name: 'customerId', dataType: ['string'], description: 'Associated customer ID' },
        { name: 'type', dataType: ['string'], description: 'Interaction type (email, call, service)' },
        { name: 'content', dataType: ['text'], description: 'Interaction content' },
        { name: 'timestamp', dataType: ['date'], description: 'Interaction timestamp' },
        { name: 'sentiment', dataType: ['string'], description: 'Sentiment analysis result' },
        { name: 'topics', dataType: ['string[]'], description: 'Extracted topics' },
        { name: 'actionItems', dataType: ['string[]'], description: 'Identified action items' },
        { name: 'aiAnalysis', dataType: ['object'], description: 'AI analysis results' }
      ],
      vectorizer: 'text2vec-transformers'
    }
  ];

  for (const schema of schemas) {
    try {
      const response = await axios.post(`${WEAVIATE_URL}/v1/schema`, schema);
      console.log(`✅ Created schema: ${schema.class}`);
    } catch (error) {
      if (error.response?.status === 422) {
        console.log(`ℹ️ Schema already exists: ${schema.class}`);
      } else {
        console.error(`❌ Error creating schema ${schema.class}:`, error.response?.data || error.message);
      }
    }
  }
}

async function createSampleData() {
  console.log('📊 Creating sample customer data...');

  const sampleCustomers = [
    {
      customerId: 'CUST_001',
      companyName: 'Biuro Rachunkowe ABC',
      contactInfo: {
        email: '<EMAIL>',
        phone: '+**************',
        address: 'ul. Marszałkowska 1, Warszawa'
      },
      businessType: 'office',
      buildingSize: 200,
      equipmentHistory: [],
      serviceHistory: [],
      communicationPreferences: {
        preferredChannels: ['email', 'phone'],
        language: 'pl'
      },
      aiInsights: {
        healthScore: 85,
        churnProbability: 0.15,
        lifetimeValue: 25000
      },
      lastUpdated: new Date().toISOString()
    },
    {
      customerId: 'CUST_002',
      companyName: 'Restauracja Smaki Warszawy',
      contactInfo: {
        email: '<EMAIL>',
        phone: '+**************',
        address: 'ul. Nowy Świat 15, Warszawa'
      },
      businessType: 'restaurant',
      buildingSize: 150,
      equipmentHistory: [],
      serviceHistory: [],
      communicationPreferences: {
        preferredChannels: ['phone', 'email'],
        language: 'pl'
      },
      aiInsights: {
        healthScore: 92,
        churnProbability: 0.08,
        lifetimeValue: 35000
      },
      lastUpdated: new Date().toISOString()
    }
  ];

  for (const customer of sampleCustomers) {
    try {
      const response = await axios.post(`${WEAVIATE_URL}/v1/objects`, {
        class: 'HVACCustomer',
        properties: customer
      });
      console.log(`✅ Created customer: ${customer.companyName} (ID: ${response.data.id})`);
    } catch (error) {
      console.error(`❌ Error creating customer ${customer.companyName}:`, error.response?.data || error.message);
    }
  }
}

async function createSampleInteractions() {
  console.log('💬 Creating sample interactions...');

  const sampleInteractions = [
    {
      interactionId: 'INT_001',
      customerId: 'CUST_001',
      type: 'email',
      content: 'Dzień dobry, mamy problem z klimatyzacją LG w biurze. Przestała chłodzić i wydaje dziwne dźwięki.',
      timestamp: new Date().toISOString(),
      sentiment: 'negative',
      topics: ['klimatyzacja', 'naprawa', 'LG', 'problem'],
      actionItems: ['schedule_service_visit', 'check_warranty', 'prepare_replacement_parts'],
      aiAnalysis: {
        urgency: 'high',
        category: 'technical_issue',
        estimatedCost: 500,
        recommendedAction: 'immediate_service_call'
      }
    },
    {
      interactionId: 'INT_002',
      customerId: 'CUST_002',
      type: 'phone',
      content: 'Dzwoniłem w sprawie serwisu klimatyzacji w restauracji. Wszystko działa dobrze, ale chciałbym umówić przegląd.',
      timestamp: new Date().toISOString(),
      sentiment: 'positive',
      topics: ['serwis', 'przegląd', 'konserwacja', 'restauracja'],
      actionItems: ['schedule_maintenance', 'send_service_proposal', 'update_service_calendar'],
      aiAnalysis: {
        urgency: 'low',
        category: 'maintenance',
        estimatedCost: 200,
        recommendedAction: 'schedule_routine_maintenance'
      }
    }
  ];

  for (const interaction of sampleInteractions) {
    try {
      const response = await axios.post(`${WEAVIATE_URL}/v1/objects`, {
        class: 'HVACInteraction',
        properties: interaction
      });
      console.log(`✅ Created interaction: ${interaction.interactionId} (ID: ${response.data.id})`);
    } catch (error) {
      console.error(`❌ Error creating interaction ${interaction.interactionId}:`, error.response?.data || error.message);
    }
  }
}

async function testSemanticSearch() {
  console.log('🔍 Testing semantic search...');

  try {
    // Test customer search
    const customerQuery = {
      query: {
        Get: {
          HVACCustomer: {
            customerId: true,
            companyName: true,
            businessType: true,
            _additional: ['id', 'certainty']
          }
        }
      }
    };

    const customerResponse = await axios.post(`${WEAVIATE_URL}/v1/graphql`, customerQuery);
    console.log('✅ Customer search results:', JSON.stringify(customerResponse.data, null, 2));

    // Test interaction search
    const interactionQuery = {
      query: {
        Get: {
          HVACInteraction: {
            interactionId: true,
            customerId: true,
            type: true,
            sentiment: true,
            topics: true,
            _additional: ['id', 'certainty']
          }
        }
      }
    };

    const interactionResponse = await axios.post(`${WEAVIATE_URL}/v1/graphql`, interactionQuery);
    console.log('✅ Interaction search results:', JSON.stringify(interactionResponse.data, null, 2));

  } catch (error) {
    console.error('❌ Error testing semantic search:', error.response?.data || error.message);
  }
}

async function main() {
  try {
    console.log('🚀 Starting Weaviate setup and testing...');
    
    // Check Weaviate connection
    const healthResponse = await axios.get(`${WEAVIATE_URL}/v1/meta`);
    console.log('✅ Weaviate is running:', healthResponse.data.version);

    // Setup schemas
    await setupWeaviateSchemas();

    // Create sample data
    await createSampleData();
    await createSampleInteractions();

    // Test semantic search
    await testSemanticSearch();

    console.log('🎉 Weaviate setup and testing completed successfully!');
    console.log('🔗 Access Weaviate console at: http://localhost:8082/v1/console');

  } catch (error) {
    console.error('❌ Error during setup:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { setupWeaviateSchemas, createSampleData, testSemanticSearch };
