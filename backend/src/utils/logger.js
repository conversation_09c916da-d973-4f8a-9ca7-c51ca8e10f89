/**
 * 📝 Enhanced Logger Utility for HVAC CRM
 * Provides structured logging with semantic intelligence context
 */

const fs = require('fs');
const path = require('path');

class Logger {
  constructor() {
    this.logDir = path.join(__dirname, '../../logs');
    this.ensureLogDirectory();
  }

  ensureLogDirectory() {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }

  formatMessage(level, message, context = {}) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      message,
      context,
      pid: process.pid
    };

    return JSON.stringify(logEntry);
  }

  writeToFile(level, formattedMessage) {
    const filename = `${level}-${new Date().toISOString().split('T')[0]}.log`;
    const filepath = path.join(this.logDir, filename);
    
    fs.appendFileSync(filepath, formattedMessage + '\n');
  }

  log(level, message, context = {}) {
    const formattedMessage = this.formatMessage(level, message, context);
    
    // Console output with colors
    const colors = {
      info: '\x1b[36m',    // Cyan
      warn: '\x1b[33m',    // Yellow
      error: '\x1b[31m',   // Red
      debug: '\x1b[35m',   // Magenta
      success: '\x1b[32m'  // Green
    };
    
    const reset = '\x1b[0m';
    const color = colors[level] || '';
    
    console.log(`${color}[${level.toUpperCase()}]${reset} ${message}`, context);
    
    // Write to file
    this.writeToFile(level, formattedMessage);
  }

  info(message, context = {}) {
    this.log('info', message, context);
  }

  warn(message, context = {}) {
    this.log('warn', message, context);
  }

  error(message, context = {}) {
    this.log('error', message, context);
  }

  debug(message, context = {}) {
    this.log('debug', message, context);
  }

  success(message, context = {}) {
    this.log('success', message, context);
  }

  // Semantic-specific logging methods
  semantic(message, context = {}) {
    this.info(`🧠 SEMANTIC: ${message}`, { ...context, category: 'semantic' });
  }

  weaviate(message, context = {}) {
    this.info(`🗄️ WEAVIATE: ${message}`, { ...context, category: 'weaviate' });
  }

  ai(message, context = {}) {
    this.info(`🤖 AI: ${message}`, { ...context, category: 'ai' });
  }

  crm(message, context = {}) {
    this.info(`💼 CRM: ${message}`, { ...context, category: 'crm' });
  }
}

// Create singleton instance
const logger = new Logger();

module.exports = logger;
