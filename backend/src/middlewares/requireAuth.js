const jwt = require('jsonwebtoken');
const Admin = require('../models/coreModels/Admin');

/**
 * AUTHENTICATION MIDDLEWARE
 * Verifies JWT tokens and ensures user authentication
 */

const requireAuth = async (req, res, next) => {
  try {
    // Get token from header
    const authHeader = req.header('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. No valid token provided.'
      });
    }
    
    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Get user from database
    const user = await Admin.findById(decoded.id).select('-password');
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. User not found.'
      });
    }
    
    if (!user.enabled) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. User account is disabled.'
      });
    }
    
    // Add user to request object
    req.user = user;
    req.userId = user._id;
    
    next();
    
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Access denied. Invalid token.'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Access denied. Token expired.'
      });
    }
    
    console.error('Auth middleware error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error during authentication.'
    });
  }
};

/**
 * OPTIONAL AUTHENTICATION MIDDLEWARE
 * Adds user info if token is present, but doesn't require authentication
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.header('Authorization');
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const user = await Admin.findById(decoded.id).select('-password');
        
        if (user && user.enabled) {
          req.user = user;
          req.userId = user._id;
        }
      } catch (error) {
        // Ignore token errors for optional auth
        console.log('Optional auth token error (ignored):', error.message);
      }
    }
    
    next();
    
  } catch (error) {
    console.error('Optional auth middleware error:', error);
    next(); // Continue even if there's an error
  }
};

/**
 * ROLE-BASED AUTHORIZATION MIDDLEWARE
 * Requires specific roles for access
 */
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. Authentication required.'
      });
    }
    
    const userRole = req.user.role || 'user';
    const allowedRoles = Array.isArray(roles) ? roles : [roles];
    
    if (!allowedRoles.includes(userRole)) {
      return res.status(403).json({
        success: false,
        message: `Access denied. Required role: ${allowedRoles.join(' or ')}`
      });
    }
    
    next();
  };
};

/**
 * ADMIN ONLY MIDDLEWARE
 * Requires admin role for access
 */
const requireAdmin = requireRole(['admin', 'super_admin']);

/**
 * MANAGER OR ADMIN MIDDLEWARE
 * Requires manager or admin role for access
 */
const requireManager = requireRole(['manager', 'admin', 'super_admin']);

module.exports = {
  requireAuth,
  optionalAuth,
  requireRole,
  requireAdmin,
  requireManager
};
