/**
 * 🚀 Gobeklitepe Semantic Framework Integration
 * Connects HVAC CRM with Weaviate vector database for advanced customer profiling
 */

const axios = require('axios');
const logger = require('@/utils/logger');

class GobeklitepeIntegration {
  constructor() {
    this.weaviateUrl = process.env.WEAVIATE_URL || 'http://localhost:8082';
    this.emailIntelligenceUrl = process.env.EMAIL_INTELLIGENCE_URL || 'http://localhost:8001';
    this.isConnected = false;
    
    logger.info('🧠 Initializing Gobeklitepe Semantic Integration...');
  }

  /**
   * Initialize connection to Weaviate and Email Intelligence
   */
  async initialize() {
    try {
      // Test Weaviate connection
      const weaviateHealth = await axios.get(`${this.weaviateUrl}/v1/meta`);
      logger.info('✅ Weaviate connection established');

      // Test Email Intelligence connection
      try {
        const emailHealth = await axios.get(`${this.emailIntelligenceUrl}/health`);
        logger.info('✅ Email Intelligence connection established');
      } catch (error) {
        logger.warn('⚠️ Email Intelligence not available, continuing without it');
      }

      this.isConnected = true;
      await this.ensureSchemas();
      
      logger.info('🚀 Gobeklitepe Integration initialized successfully');
      return true;
    } catch (error) {
      logger.error('❌ Failed to initialize Gobeklitepe Integration:', error.message);
      return false;
    }
  }

  /**
   * Ensure required Weaviate schemas exist
   */
  async ensureSchemas() {
    const schemas = [
      {
        class: 'HVACCustomer',
        description: 'Enhanced HVAC customer profiles with semantic intelligence',
        properties: [
          { name: 'customerId', dataType: ['string'], description: 'Unique customer identifier' },
          { name: 'companyName', dataType: ['string'], description: 'Company or customer name' },
          { name: 'contactInfo', dataType: ['object'], description: 'Contact information' },
          { name: 'businessType', dataType: ['string'], description: 'Type of business' },
          { name: 'buildingSize', dataType: ['number'], description: 'Building size in square meters' },
          { name: 'equipmentHistory', dataType: ['object'], description: 'HVAC equipment history' },
          { name: 'serviceHistory', dataType: ['object'], description: 'Service interaction history' },
          { name: 'communicationPreferences', dataType: ['object'], description: 'Communication preferences' },
          { name: 'aiInsights', dataType: ['object'], description: 'AI-generated customer insights' },
          { name: 'lastUpdated', dataType: ['date'], description: 'Last profile update timestamp' }
        ],
        vectorizer: 'text2vec-transformers'
      },
      {
        class: 'HVACInteraction',
        description: 'Customer interactions with semantic analysis',
        properties: [
          { name: 'interactionId', dataType: ['string'], description: 'Unique interaction identifier' },
          { name: 'customerId', dataType: ['string'], description: 'Associated customer ID' },
          { name: 'type', dataType: ['string'], description: 'Interaction type (email, call, service)' },
          { name: 'content', dataType: ['text'], description: 'Interaction content' },
          { name: 'timestamp', dataType: ['date'], description: 'Interaction timestamp' },
          { name: 'sentiment', dataType: ['string'], description: 'Sentiment analysis result' },
          { name: 'topics', dataType: ['string[]'], description: 'Extracted topics' },
          { name: 'actionItems', dataType: ['string[]'], description: 'Identified action items' },
          { name: 'aiAnalysis', dataType: ['object'], description: 'AI analysis results' }
        ],
        vectorizer: 'text2vec-transformers'
      }
    ];

    for (const schema of schemas) {
      try {
        await axios.post(`${this.weaviateUrl}/v1/schema`, schema);
        logger.info(`✅ Created schema: ${schema.class}`);
      } catch (error) {
        if (error.response?.status === 422) {
          logger.info(`ℹ️ Schema already exists: ${schema.class}`);
        } else {
          logger.error(`❌ Error creating schema ${schema.class}:`, error.message);
        }
      }
    }
  }

  /**
   * Create or update customer profile in Weaviate
   */
  async upsertCustomerProfile(customerData) {
    if (!this.isConnected) {
      logger.warn('⚠️ Gobeklitepe not connected, skipping profile upsert');
      return null;
    }

    try {
      const profileData = {
        customerId: customerData._id.toString(),
        companyName: customerData.company || customerData.name,
        contactInfo: {
          email: customerData.email,
          phone: customerData.phone,
          address: customerData.address
        },
        businessType: customerData.buildingType || 'unknown',
        buildingSize: customerData.buildingSize || 0,
        equipmentHistory: customerData.equipmentHistory || [],
        serviceHistory: customerData.serviceHistory || [],
        communicationPreferences: customerData.communicationPreferences || {},
        aiInsights: customerData.aiInsights || {},
        lastUpdated: new Date().toISOString()
      };

      // Check if profile exists
      const existingProfile = await this.searchCustomerProfiles({
        where: {
          path: ['customerId'],
          operator: 'Equal',
          valueString: profileData.customerId
        }
      });

      if (existingProfile.length > 0) {
        // Update existing profile
        const profileId = existingProfile[0].id;
        await axios.patch(`${this.weaviateUrl}/v1/objects/${profileId}`, {
          class: 'HVACCustomer',
          properties: profileData
        });
        logger.info(`✅ Updated customer profile: ${profileData.customerId}`);
        return profileId;
      } else {
        // Create new profile
        const response = await axios.post(`${this.weaviateUrl}/v1/objects`, {
          class: 'HVACCustomer',
          properties: profileData
        });
        logger.info(`✅ Created customer profile: ${profileData.customerId}`);
        return response.data.id;
      }
    } catch (error) {
      logger.error('❌ Error upserting customer profile:', error.message);
      return null;
    }
  }

  /**
   * Store interaction with semantic analysis
   */
  async storeInteraction(interactionData) {
    if (!this.isConnected) {
      logger.warn('⚠️ Gobeklitepe not connected, skipping interaction storage');
      return null;
    }

    try {
      const interaction = {
        interactionId: interactionData.id || Date.now().toString(),
        customerId: interactionData.customerId,
        type: interactionData.type,
        content: interactionData.content,
        timestamp: interactionData.timestamp || new Date().toISOString(),
        sentiment: interactionData.sentiment || 'neutral',
        topics: interactionData.topics || [],
        actionItems: interactionData.actionItems || [],
        aiAnalysis: interactionData.aiAnalysis || {}
      };

      const response = await axios.post(`${this.weaviateUrl}/v1/objects`, {
        class: 'HVACInteraction',
        properties: interaction
      });

      logger.info(`✅ Stored interaction: ${interaction.interactionId}`);
      return response.data.id;
    } catch (error) {
      logger.error('❌ Error storing interaction:', error.message);
      return null;
    }
  }

  /**
   * Search customer profiles using semantic search
   */
  async searchCustomerProfiles(query) {
    if (!this.isConnected) {
      return [];
    }

    try {
      const searchQuery = {
        query: {
          Get: {
            HVACCustomer: {
              where: query.where,
              limit: query.limit || 10,
              _additional: ['id', 'certainty']
            }
          }
        }
      };

      const response = await axios.post(`${this.weaviateUrl}/v1/graphql`, searchQuery);
      return response.data.data.Get.HVACCustomer || [];
    } catch (error) {
      logger.error('❌ Error searching customer profiles:', error.message);
      return [];
    }
  }

  /**
   * Get customer insights using semantic analysis
   */
  async getCustomerInsights(customerId) {
    if (!this.isConnected) {
      return null;
    }

    try {
      // Get customer profile
      const profiles = await this.searchCustomerProfiles({
        where: {
          path: ['customerId'],
          operator: 'Equal',
          valueString: customerId
        }
      });

      if (profiles.length === 0) {
        return null;
      }

      // Get recent interactions
      const interactions = await this.searchInteractions({
        where: {
          path: ['customerId'],
          operator: 'Equal',
          valueString: customerId
        },
        limit: 20
      });

      return {
        profile: profiles[0],
        recentInteractions: interactions,
        insights: {
          totalInteractions: interactions.length,
          sentimentTrend: this.analyzeSentimentTrend(interactions),
          topTopics: this.extractTopTopics(interactions),
          actionItems: this.extractActionItems(interactions)
        }
      };
    } catch (error) {
      logger.error('❌ Error getting customer insights:', error.message);
      return null;
    }
  }

  /**
   * Search interactions
   */
  async searchInteractions(query) {
    try {
      const searchQuery = {
        query: {
          Get: {
            HVACInteraction: {
              where: query.where,
              limit: query.limit || 10,
              _additional: ['id', 'certainty']
            }
          }
        }
      };

      const response = await axios.post(`${this.weaviateUrl}/v1/graphql`, searchQuery);
      return response.data.data.Get.HVACInteraction || [];
    } catch (error) {
      logger.error('❌ Error searching interactions:', error.message);
      return [];
    }
  }

  /**
   * Analyze sentiment trend from interactions
   */
  analyzeSentimentTrend(interactions) {
    if (interactions.length === 0) return 'neutral';
    
    const sentiments = interactions.map(i => i.sentiment);
    const positive = sentiments.filter(s => s === 'positive').length;
    const negative = sentiments.filter(s => s === 'negative').length;
    
    if (positive > negative) return 'improving';
    if (negative > positive) return 'declining';
    return 'stable';
  }

  /**
   * Extract top topics from interactions
   */
  extractTopTopics(interactions) {
    const topicCounts = {};
    interactions.forEach(interaction => {
      if (interaction.topics) {
        interaction.topics.forEach(topic => {
          topicCounts[topic] = (topicCounts[topic] || 0) + 1;
        });
      }
    });

    return Object.entries(topicCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([topic, count]) => ({ topic, count }));
  }

  /**
   * Extract action items from interactions
   */
  extractActionItems(interactions) {
    const actionItems = [];
    interactions.forEach(interaction => {
      if (interaction.actionItems) {
        actionItems.push(...interaction.actionItems);
      }
    });
    return actionItems.slice(0, 10); // Return top 10 action items
  }

  /**
   * Process email intelligence data
   */
  async processEmailIntelligence(emailData) {
    try {
      // Send to Email Intelligence service for processing
      const response = await axios.post(`${this.emailIntelligenceUrl}/process-email`, emailData);
      
      if (response.data.success) {
        // Store the processed interaction
        await this.storeInteraction({
          customerId: response.data.customerId,
          type: 'email',
          content: emailData.content,
          sentiment: response.data.sentiment,
          topics: response.data.topics,
          actionItems: response.data.actionItems,
          aiAnalysis: response.data.analysis
        });

        logger.info('✅ Email intelligence processed and stored');
        return response.data;
      }
    } catch (error) {
      logger.error('❌ Error processing email intelligence:', error.message);
      return null;
    }
  }

  /**
   * Health check
   */
  async healthCheck() {
    try {
      const weaviateHealth = await axios.get(`${this.weaviateUrl}/v1/.well-known/ready`);
      return {
        weaviate: weaviateHealth.status === 200,
        connected: this.isConnected
      };
    } catch (error) {
      return {
        weaviate: false,
        connected: false,
        error: error.message
      };
    }
  }
}

module.exports = GobeklitepeIntegration;
