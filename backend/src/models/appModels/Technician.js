const mongoose = require('mongoose');

const technicianSchema = new mongoose.Schema({
  // Basic Information
  name: {
    type: String,
    required: true,
    trim: true
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  phone: {
    type: String,
    required: true,
    trim: true
  },
  employeeId: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  
  // Professional Information
  specializations: [{
    type: String,
    enum: [
      'air_conditioning',
      'heating',
      'ventilation',
      'refrigeration',
      'heat_pumps',
      'boilers',
      'ductwork',
      'electrical',
      'plumbing',
      'controls',
      'commercial',
      'residential',
      'industrial'
    ]
  }],
  
  certifications: [{
    name: String,
    issuedBy: String,
    issuedDate: Date,
    expiryDate: Date,
    certificateNumber: String,
    isActive: {
      type: Boolean,
      default: true
    }
  }],
  
  experienceLevel: {
    type: String,
    enum: ['junior', 'intermediate', 'senior', 'expert'],
    default: 'intermediate'
  },
  
  yearsOfExperience: {
    type: Number,
    default: 0
  },
  
  // Availability and Scheduling
  workingHours: {
    monday: {
      start: { type: String, default: '08:00' },
      end: { type: String, default: '17:00' },
      available: { type: Boolean, default: true }
    },
    tuesday: {
      start: { type: String, default: '08:00' },
      end: { type: String, default: '17:00' },
      available: { type: Boolean, default: true }
    },
    wednesday: {
      start: { type: String, default: '08:00' },
      end: { type: String, default: '17:00' },
      available: { type: Boolean, default: true }
    },
    thursday: {
      start: { type: String, default: '08:00' },
      end: { type: String, default: '17:00' },
      available: { type: Boolean, default: true }
    },
    friday: {
      start: { type: String, default: '08:00' },
      end: { type: String, default: '17:00' },
      available: { type: Boolean, default: true }
    },
    saturday: {
      start: { type: String, default: '09:00' },
      end: { type: String, default: '15:00' },
      available: { type: Boolean, default: false }
    },
    sunday: {
      start: { type: String, default: '09:00' },
      end: { type: String, default: '15:00' },
      available: { type: Boolean, default: false }
    }
  },
  
  currentLocation: {
    type: {
      type: String,
      enum: ['Point'],
      default: 'Point'
    },
    coordinates: {
      type: [Number], // [longitude, latitude]
      default: [21.0122, 52.2297] // Warsaw coordinates as default
    },
    address: String,
    lastUpdated: {
      type: Date,
      default: Date.now
    }
  },
  
  serviceAreas: [{
    district: String,
    travelTime: Number, // minutes
    preferenceLevel: {
      type: Number,
      min: 1,
      max: 5,
      default: 3
    }
  }],
  
  // Performance Metrics
  performanceMetrics: {
    averageRating: {
      type: Number,
      min: 1,
      max: 5,
      default: 5
    },
    totalJobsCompleted: {
      type: Number,
      default: 0
    },
    onTimeCompletionRate: {
      type: Number,
      min: 0,
      max: 100,
      default: 100
    },
    customerSatisfactionScore: {
      type: Number,
      min: 1,
      max: 5,
      default: 5
    },
    averageJobDuration: {
      type: Number, // in hours
      default: 2
    },
    firstTimeFixRate: {
      type: Number,
      min: 0,
      max: 100,
      default: 90
    }
  },
  
  // Current Status
  status: {
    type: String,
    enum: ['available', 'busy', 'on_break', 'off_duty', 'vacation', 'sick'],
    default: 'available'
  },
  
  currentWorkload: {
    type: Number,
    min: 0,
    max: 100,
    default: 0
  },
  
  // Equipment and Tools
  tools: [{
    name: String,
    type: String,
    serialNumber: String,
    lastMaintenance: Date,
    nextMaintenance: Date
  }],
  
  vehicle: {
    make: String,
    model: String,
    year: Number,
    licensePlate: String,
    fuelType: String,
    lastService: Date,
    nextService: Date
  },
  
  // Emergency and Contact
  emergencyContact: {
    name: String,
    relationship: String,
    phone: String,
    email: String
  },
  
  // AI Assignment Factors
  aiAssignmentFactors: {
    skillMatchWeight: {
      type: Number,
      default: 0.3
    },
    locationWeight: {
      type: Number,
      default: 0.25
    },
    workloadWeight: {
      type: Number,
      default: 0.2
    },
    performanceWeight: {
      type: Number,
      default: 0.15
    },
    availabilityWeight: {
      type: Number,
      default: 0.1
    }
  },
  
  // Preferences
  preferences: {
    preferredJobTypes: [String],
    maxDailyJobs: {
      type: Number,
      default: 6
    },
    maxTravelDistance: {
      type: Number,
      default: 50 // kilometers
    },
    overtimeAvailable: {
      type: Boolean,
      default: false
    }
  },
  
  // Standard fields
  enabled: {
    type: Boolean,
    default: true
  },
  removed: {
    type: Boolean,
    default: false
  },
  
  // Audit fields
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
technicianSchema.index({ email: 1 }, { unique: true });
technicianSchema.index({ employeeId: 1 }, { unique: true });
technicianSchema.index({ status: 1 });
technicianSchema.index({ specializations: 1 });
technicianSchema.index({ currentLocation: '2dsphere' });
technicianSchema.index({ enabled: 1, removed: 1 });

// Compound indexes
technicianSchema.index({ status: 1, currentWorkload: 1 });
technicianSchema.index({ specializations: 1, status: 1 });

// Virtual for full name display
technicianSchema.virtual('displayName').get(function() {
  return `${this.name} (${this.employeeId})`;
});

// Virtual for availability status
technicianSchema.virtual('isAvailable').get(function() {
  return this.status === 'available' && this.currentWorkload < 100;
});

// Virtual for current day availability
technicianSchema.virtual('todayAvailability').get(function() {
  const today = new Date().toLocaleLowerCase();
  const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
  const dayName = dayNames[new Date().getDay()];
  return this.workingHours[dayName];
});

// Methods
technicianSchema.methods.calculateSkillMatch = function(requiredSkills) {
  if (!requiredSkills || requiredSkills.length === 0) return 1;
  
  const matchingSkills = requiredSkills.filter(skill => 
    this.specializations.includes(skill)
  );
  
  return matchingSkills.length / requiredSkills.length;
};

technicianSchema.methods.calculateDistanceScore = function(jobLocation) {
  // Simple distance calculation - in real implementation, use proper geospatial queries
  if (!jobLocation || !this.currentLocation.coordinates) return 0.5;
  
  // Placeholder for distance calculation
  // In real implementation, calculate actual distance
  return Math.random() * 0.5 + 0.5; // Random score between 0.5-1
};

technicianSchema.methods.updateLocation = function(coordinates, address) {
  this.currentLocation.coordinates = coordinates;
  this.currentLocation.address = address;
  this.currentLocation.lastUpdated = new Date();
  return this.save();
};

technicianSchema.methods.updateWorkload = function(percentage) {
  this.currentWorkload = Math.max(0, Math.min(100, percentage));
  return this.save();
};

// Static methods
technicianSchema.statics.findAvailable = function() {
  return this.find({
    status: 'available',
    currentWorkload: { $lt: 100 },
    enabled: true,
    removed: false
  });
};

technicianSchema.statics.findBySpecialization = function(specialization) {
  return this.find({
    specializations: specialization,
    enabled: true,
    removed: false
  });
};

technicianSchema.statics.findNearLocation = function(coordinates, maxDistance = 50000) {
  return this.find({
    currentLocation: {
      $near: {
        $geometry: {
          type: 'Point',
          coordinates: coordinates
        },
        $maxDistance: maxDistance // meters
      }
    },
    enabled: true,
    removed: false
  });
};

module.exports = mongoose.model('Technician', technicianSchema);
