const createCRUDController = require('@/controllers/middlewaresControllers/createCRUDController');
const { calculate } = require('@/helpers');

const ServiceOrder = require('@/models/appModels/ServiceOrder');
const technicianAssignmentService = require('@/services/technicianAssignmentService');

const serviceOrderController = createCRUDController('ServiceOrder');

// Override methods for HVAC-specific functionality
serviceOrderController.create = async (req, res) => {
  try {
    const { body } = req;
    
    // Auto-assign based on type and priority using AI
    if (!body.assignedTechnician && body.type && body.priority) {
      console.log('🤖 Attempting AI-powered technician assignment...');

      try {
        // Create a temporary service order object for assignment calculation
        const tempServiceOrder = {
          type: body.type,
          priority: body.priority,
          requiredSkills: body.requiredSkills || getRequiredSkillsFromType(body.type),
          location: body.location,
          scheduledDate: body.scheduledDate ? new Date(body.scheduledDate) : new Date(),
          estimatedDuration: body.estimatedDuration
        };

        const assignment = await technicianAssignmentService.assignBestTechnician(tempServiceOrder);

        if (assignment) {
          body.assignedTechnician = assignment.technician._id;
          body.assignmentReasoning = assignment.reasoning;
          body.assignmentScore = assignment.score;
          console.log(`✅ AI assigned technician: ${assignment.technician.name} (Score: ${assignment.score.toFixed(2)})`);
        } else {
          console.log('⚠️ No suitable technician found by AI, leaving for manual assignment');
        }
      } catch (aiError) {
        console.error('❌ AI assignment failed, falling back to manual:', aiError.message);
        // Continue with manual assignment (no technician assigned)
      }
    }
    
    // Set estimated duration based on type
    if (!body.estimatedDuration) {
      const durationMap = {
        'maintenance': 2,
        'repair': 4,
        'installation': 8,
        'inspection': 1,
        'emergency': 3
      };
      body.estimatedDuration = durationMap[body.type] || 2;
    }
    
    const result = await ServiceOrder.create(body);
    return res.status(200).json({
      success: true,
      result,
      message: 'Service order created successfully',
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      result: null,
      message: error.message,
    });
  }
};

serviceOrderController.summary = async (req, res) => {
  try {
    const ServiceOrder = require('@/models/appModels/ServiceOrder');
    
    // Get service order statistics
    const totalOrders = await ServiceOrder.countDocuments({ removed: false });
    const activeOrders = await ServiceOrder.countDocuments({ 
      removed: false, 
      stage: { $nin: ['COMPLETED', 'BILLED', 'CLOSED'] }
    });
    
    // Orders by stage
    const ordersByStage = await ServiceOrder.aggregate([
      { $match: { removed: false } },
      { $group: { _id: '$stage', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    
    // Orders by type
    const ordersByType = await ServiceOrder.aggregate([
      { $match: { removed: false } },
      { $group: { _id: '$type', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    
    // Orders by priority
    const ordersByPriority = await ServiceOrder.aggregate([
      { $match: { removed: false } },
      { $group: { _id: '$priority', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    
    // Revenue statistics
    const revenueStats = await ServiceOrder.aggregate([
      { $match: { removed: false, actualCost: { $gt: 0 } } },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$actualCost' },
          avgOrderValue: { $avg: '$actualCost' },
          minOrderValue: { $min: '$actualCost' },
          maxOrderValue: { $max: '$actualCost' }
        }
      }
    ]);
    
    // Overdue orders (scheduled but not completed)
    const now = new Date();
    const overdueOrders = await ServiceOrder.countDocuments({
      removed: false,
      scheduledDate: { $lt: now },
      stage: { $nin: ['COMPLETED', 'BILLED', 'CLOSED'] }
    });
    
    // Customer satisfaction average
    const satisfactionStats = await ServiceOrder.aggregate([
      { $match: { removed: false, customerSatisfaction: { $exists: true } } },
      {
        $group: {
          _id: null,
          avgSatisfaction: { $avg: '$customerSatisfaction' },
          totalRatings: { $sum: 1 }
        }
      }
    ]);
    
    return res.status(200).json({
      success: true,
      result: {
        totalOrders,
        activeOrders,
        ordersByStage,
        ordersByType,
        ordersByPriority,
        revenueStats: revenueStats[0] || {},
        overdueOrders,
        satisfactionStats: satisfactionStats[0] || {}
      },
      message: 'Service order summary retrieved successfully',
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      result: null,
      message: error.message,
    });
  }
};

// Get orders by technician
serviceOrderController.getByTechnician = async (req, res) => {
  try {
    const { technicianId } = req.params;
    
    const orders = await ServiceOrder.find({ 
      assignedTechnician: technicianId, 
      removed: false 
    })
    .populate('client', 'name phone address')
    .populate('equipment', 'name type manufacturer')
    .sort({ scheduledDate: 1 });
    
    return res.status(200).json({
      success: true,
      result: orders,
      message: 'Technician orders retrieved successfully',
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      result: null,
      message: error.message,
    });
  }
};

// Update stage
serviceOrderController.updateStage = async (req, res) => {
  try {
    const { id } = req.params;
    const { stage, notes } = req.body;
    
    const updateData = { 
      stage,
      updated: new Date()
    };
    
    // Auto-set completion date when moving to COMPLETED
    if (stage === 'COMPLETED') {
      updateData.completedDate = new Date();
      updateData.actualEndTime = new Date();
    }
    
    // Auto-set start time when moving to IN_PROGRESS
    if (stage === 'IN_PROGRESS') {
      updateData.actualStartTime = new Date();
    }
    
    const serviceOrder = await ServiceOrder.findByIdAndUpdate(
      id,
      updateData,
      { new: true }
    );
    
    if (!serviceOrder) {
      return res.status(404).json({
        success: false,
        result: null,
        message: 'Service order not found',
      });
    }
    
    return res.status(200).json({
      success: true,
      result: serviceOrder,
      message: 'Service order stage updated successfully',
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      result: null,
      message: error.message,
    });
  }
};

// Get kanban board data
serviceOrderController.getKanbanBoard = async (req, res) => {
  try {
    const stages = [
      'BACKLOG', 'SCHEDULED', 'IN_PROGRESS', 'PENDING_PARTS', 
      'QUALITY_CHECK', 'COMPLETED', 'BILLED', 'CLOSED'
    ];
    
    const kanbanData = {};
    
    for (const stage of stages) {
      const orders = await ServiceOrder.find({ 
        stage, 
        removed: false 
      })
      .populate('client', 'name phone')
      .populate('assignedTechnician', 'name')
      .sort({ priority: -1, scheduledDate: 1 });
      
      kanbanData[stage] = orders;
    }
    
    return res.status(200).json({
      success: true,
      result: kanbanData,
      message: 'Kanban board data retrieved successfully',
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      result: null,
      message: error.message,
    });
  }
};

// Add new method for getting technician assignment recommendations
serviceOrderController.getAssignmentRecommendations = async (req, res) => {
  try {
    const { serviceOrderData } = req.body;

    const recommendations = await technicianAssignmentService.getAssignmentRecommendations(
      serviceOrderData,
      5 // Get top 5 recommendations
    );

    return res.status(200).json({
      success: true,
      result: recommendations,
      message: 'Technician assignment recommendations retrieved successfully',
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      result: null,
      message: error.message,
    });
  }
};

// Helper function to get required skills based on service order type
function getRequiredSkillsFromType(type) {
  const skillMap = {
    'maintenance': ['air_conditioning', 'heating', 'ventilation'],
    'repair': ['air_conditioning', 'heating', 'electrical'],
    'installation': ['air_conditioning', 'heating', 'ductwork', 'electrical'],
    'inspection': ['air_conditioning', 'heating', 'ventilation'],
    'emergency': ['air_conditioning', 'heating', 'electrical'],
    'hvac_service': ['air_conditioning', 'heating', 'ventilation'],
    'boiler_service': ['boilers', 'heating', 'plumbing'],
    'heat_pump_service': ['heat_pumps', 'heating', 'electrical'],
    'commercial_service': ['commercial', 'air_conditioning', 'heating'],
    'residential_service': ['residential', 'air_conditioning', 'heating']
  };

  return skillMap[type] || ['air_conditioning', 'heating'];
}

module.exports = serviceOrderController;
