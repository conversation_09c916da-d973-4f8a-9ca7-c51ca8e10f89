/**
 * INVOICE ANALYSIS CONTROLLER
 * API endpoints for AI-powered invoice analysis from email attachments
 * Part of HVAC CRM Email Intelligence System
 */

const { validationResult } = require('express-validator');

// Import services
const invoiceAnalysisService = require('@/services/invoiceAnalysisService');
const emailService = require('@/services/emailService');

// Import models
const Email = require('@/models/appModels/Email');
const Invoice = require('@/models/appModels/Invoice');

/**
 * MAIN ANALYSIS ENDPOINTS
 */

// Analyze specific invoice attachment
const analyzeInvoiceAttachment = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const { emailId, attachmentId } = req.params;

    console.log(`🧾 Starting invoice analysis for email ${emailId}, attachment ${attachmentId}`);

    const result = await invoiceAnalysisService.analyzeInvoiceFromEmail(emailId, attachmentId);

    res.status(200).json({
      success: true,
      result,
      message: 'Invoice analysis completed successfully'
    });

  } catch (error) {
    console.error(`❌ Invoice analysis failed: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'Invoice analysis failed',
      error: error.message
    });
  }
};

// Batch analyze all pending invoice attachments
const batchAnalyzeInvoices = async (req, res) => {
  try {
    const { daysBack = 30 } = req.query;

    console.log(`🔄 Starting batch invoice analysis for last ${daysBack} days`);

    const result = await invoiceAnalysisService.batchAnalyzeInvoices(parseInt(daysBack));

    res.status(200).json({
      success: true,
      result,
      message: `Batch analysis completed: ${result.totalProcessed} invoices processed`
    });

  } catch (error) {
    console.error(`❌ Batch invoice analysis failed: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'Batch invoice analysis failed',
      error: error.message
    });
  }
};

// Get invoice analysis dashboard data
const getInvoiceAnalysisDashboard = async (req, res) => {
  try {
    const stats = await invoiceAnalysisService.getInvoiceAnalysisStats();

    // Get pending analysis count
    const pendingAnalysis = await Email.countDocuments({
      'attachments.0': { $exists: true },
      'invoiceAnalysis.analyzed': { $ne: true }
    });

    // Get recent activity
    const recentActivity = await Email.find({
      'invoiceAnalysis.analyzed': true
    })
    .sort({ 'invoiceAnalysis.analyzedAt': -1 })
    .limit(10)
    .select('subject sender invoiceAnalysis')
    .populate('invoiceAnalysis.invoiceId', 'number total');

    const dashboardData = {
      ...stats,
      pendingAnalysis,
      recentActivity,
      systemHealth: {
        ocrService: 'healthy',
        aiService: 'healthy',
        lastAnalysis: stats.recentAnalyses[0]?.analysisData?.analyzed_at || null
      }
    };

    res.status(200).json({
      success: true,
      result: dashboardData,
      message: 'Invoice analysis dashboard data retrieved'
    });

  } catch (error) {
    console.error(`❌ Failed to get dashboard data: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve dashboard data',
      error: error.message
    });
  }
};

/**
 * INVOICE MANAGEMENT ENDPOINTS
 */

// Get analyzed invoices with filtering
const getAnalyzedInvoices = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      category, 
      dateFrom, 
      dateTo,
      minAmount,
      maxAmount,
      search 
    } = req.query;

    // Build query
    let query = { 'analysisData.source_email': { $exists: true } };

    if (category && category !== 'all') {
      query.hvacCategory = category;
    }

    if (dateFrom || dateTo) {
      query.date = {};
      if (dateFrom) query.date.$gte = new Date(dateFrom);
      if (dateTo) query.date.$lte = new Date(dateTo);
    }

    if (minAmount || maxAmount) {
      query.total = {};
      if (minAmount) query.total.$gte = parseFloat(minAmount);
      if (maxAmount) query.total.$lte = parseFloat(maxAmount);
    }

    if (search) {
      query.$or = [
        { number: { $regex: search, $options: 'i' } },
        { 'analysisData.analysis_notes': { $regex: search, $options: 'i' } }
      ];
    }

    // Execute query with pagination
    const invoices = await Invoice.find(query)
      .populate('client', 'name email')
      .sort({ 'analysisData.analyzed_at': -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .select('number date total hvacCategory equipmentBrands analysisData client');

    const total = await Invoice.countDocuments(query);

    res.status(200).json({
      success: true,
      result: {
        invoices,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      },
      message: 'Analyzed invoices retrieved successfully'
    });

  } catch (error) {
    console.error(`❌ Failed to get analyzed invoices: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve analyzed invoices',
      error: error.message
    });
  }
};

// Get invoice analysis details
const getInvoiceAnalysisDetails = async (req, res) => {
  try {
    const { invoiceId } = req.params;

    const invoice = await Invoice.findById(invoiceId)
      .populate('client', 'name email address taxId')
      .populate('analysisData.source_email', 'subject sender senderEmail receivedAt');

    if (!invoice) {
      return res.status(404).json({
        success: false,
        message: 'Invoice not found'
      });
    }

    // Get original email attachment if available
    let originalAttachment = null;
    if (invoice.analysisData?.source_email) {
      const email = await Email.findById(invoice.analysisData.source_email);
      if (email && invoice.analysisData.source_attachment) {
        originalAttachment = email.attachments.find(
          att => att._id.toString() === invoice.analysisData.source_attachment.toString()
        );
      }
    }

    const detailsData = {
      invoice,
      originalAttachment: originalAttachment ? {
        filename: originalAttachment.filename,
        contentType: originalAttachment.contentType,
        size: originalAttachment.size
      } : null,
      analysisMetadata: {
        confidence: invoice.analysisData?.confidence_score,
        aiModel: invoice.analysisData?.ai_model,
        analyzedAt: invoice.analysisData?.analyzed_at,
        notes: invoice.analysisData?.analysis_notes
      }
    };

    res.status(200).json({
      success: true,
      result: detailsData,
      message: 'Invoice analysis details retrieved'
    });

  } catch (error) {
    console.error(`❌ Failed to get invoice details: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve invoice details',
      error: error.message
    });
  }
};

/**
 * UTILITY ENDPOINTS
 */

// Get pending invoice attachments for analysis
const getPendingInvoiceAttachments = async (req, res) => {
  try {
    const { limit = 50 } = req.query;

    const emails = await Email.find({
      'attachments.0': { $exists: true },
      'invoiceAnalysis.analyzed': { $ne: true }
    })
    .sort({ receivedAt: -1 })
    .limit(parseInt(limit))
    .select('subject sender receivedAt attachments');

    // Filter emails with invoice-like attachments
    const pendingInvoices = [];
    
    for (const email of emails) {
      for (const attachment of email.attachments) {
        if (invoiceAnalysisService.isInvoiceAttachment(attachment)) {
          pendingInvoices.push({
            emailId: email._id,
            attachmentId: attachment._id,
            emailSubject: email.subject,
            emailSender: email.sender,
            emailDate: email.receivedAt,
            filename: attachment.filename,
            contentType: attachment.contentType,
            size: attachment.size
          });
        }
      }
    }

    res.status(200).json({
      success: true,
      result: {
        pendingInvoices,
        totalCount: pendingInvoices.length
      },
      message: 'Pending invoice attachments retrieved'
    });

  } catch (error) {
    console.error(`❌ Failed to get pending attachments: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve pending attachments',
      error: error.message
    });
  }
};

// Reanalyze invoice with updated AI
const reanalyzeInvoice = async (req, res) => {
  try {
    const { invoiceId } = req.params;

    const invoice = await Invoice.findById(invoiceId);
    if (!invoice || !invoice.analysisData?.source_email) {
      return res.status(404).json({
        success: false,
        message: 'Invoice not found or not from email analysis'
      });
    }

    const emailId = invoice.analysisData.source_email;
    const attachmentId = invoice.analysisData.source_attachment;

    // Delete old invoice record
    await Invoice.findByIdAndDelete(invoiceId);

    // Reanalyze
    const result = await invoiceAnalysisService.analyzeInvoiceFromEmail(emailId, attachmentId);

    res.status(200).json({
      success: true,
      result,
      message: 'Invoice reanalyzed successfully'
    });

  } catch (error) {
    console.error(`❌ Invoice reanalysis failed: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'Invoice reanalysis failed',
      error: error.message
    });
  }
};

module.exports = {
  analyzeInvoiceAttachment,
  batchAnalyzeInvoices,
  getInvoiceAnalysisDashboard,
  getAnalyzedInvoices,
  getInvoiceAnalysisDetails,
  getPendingInvoiceAttachments,
  reanalyzeInvoice
};
