const emailProcessingService = require('../../services/emailProcessingService');
const transcriptionService = require('../../services/transcriptionService');
const Email = require('../../models/appModels/Email');
const { validationResult } = require('express-validator');

/**
 * EMA<PERSON> PROCESSING CONTROLLER
 * Handles all email processing operations including transcriptions and AI analysis
 */

/**
 * PROCESS ALL EMAILS
 * Main endpoint to process emails from both accounts
 */
const processAllEmails = async (req, res) => {
  try {
    console.log('🚀 Starting email processing pipeline...');
    
    const result = await emailProcessingService.processAllEmails();
    
    res.status(200).json({
      success: true,
      message: 'Email processing completed successfully',
      data: result,
      timestamp: new Date()
    });
    
  } catch (error) {
    console.error('❌ Email processing error:', error);
    res.status(500).json({
      success: false,
      message: 'Email processing failed',
      error: error.message,
      timestamp: new Date()
    });
  }
};

/**
 * PROCESS ALL TRANSCRIPTIONS
 * Transcribes all M4A files from emails
 */
const processAllTranscriptions = async (req, res) => {
  try {
    console.log('🎵 Starting transcription processing...');
    
    const result = await transcriptionService.processAllTranscriptions();
    
    res.status(200).json({
      success: true,
      message: 'Transcription processing completed successfully',
      data: result,
      timestamp: new Date()
    });
    
  } catch (error) {
    console.error('❌ Transcription processing error:', error);
    res.status(500).json({
      success: false,
      message: 'Transcription processing failed',
      error: error.message,
      timestamp: new Date()
    });
  }
};

/**
 * GET EMAIL PROCESSING STATUS
 * Returns current status of email processing pipeline
 */
const getProcessingStatus = async (req, res) => {
  try {
    // Get email statistics
    const totalEmails = await Email.countDocuments();
    const processedEmails = await Email.countDocuments({ processedAt: { $exists: true } });
    const emailsWithM4A = await Email.countDocuments({ 'attachments.isM4A': true });
    const transcribedM4A = await Email.countDocuments({ 'attachments.transcribed': true });
    const emailsWithInvoices = await Email.countDocuments({ 'invoiceAnalysis.analyzed': true });
    
    // Get recent processing activity
    const recentEmails = await Email.find()
      .sort({ receivedAt: -1 })
      .limit(10)
      .select('subject senderEmail receivedAt processedAt attachments transcriptions invoiceAnalysis');
    
    // Calculate processing rates
    const processingRate = totalEmails > 0 ? (processedEmails / totalEmails * 100).toFixed(1) : 0;
    const transcriptionRate = emailsWithM4A > 0 ? (transcribedM4A / emailsWithM4A * 100).toFixed(1) : 0;
    
    res.status(200).json({
      success: true,
      data: {
        statistics: {
          totalEmails,
          processedEmails,
          emailsWithM4A,
          transcribedM4A,
          emailsWithInvoices,
          processingRate: `${processingRate}%`,
          transcriptionRate: `${transcriptionRate}%`
        },
        recentActivity: recentEmails,
        lastUpdated: new Date()
      }
    });
    
  } catch (error) {
    console.error('❌ Error getting processing status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get processing status',
      error: error.message
    });
  }
};

/**
 * GET EMAIL DETAILS
 * Returns detailed information about a specific email
 */
const getEmailDetails = async (req, res) => {
  try {
    const { emailId } = req.params;
    
    const email = await Email.findById(emailId)
      .populate('relatedClient')
      .populate('relatedOpportunity')
      .populate('relatedServiceTicket')
      .populate('relatedQuote');
    
    if (!email) {
      return res.status(404).json({
        success: false,
        message: 'Email not found'
      });
    }
    
    res.status(200).json({
      success: true,
      data: email
    });
    
  } catch (error) {
    console.error('❌ Error getting email details:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get email details',
      error: error.message
    });
  }
};

/**
 * GET TRANSCRIPTIONS
 * Returns all transcriptions with optional filtering
 */
const getTranscriptions = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      search = '', 
      sentiment = '', 
      urgency = '',
      dateFrom = '',
      dateTo = ''
    } = req.query;
    
    // Build query
    const query = {
      transcriptions: { $exists: true, $not: { $size: 0 } }
    };
    
    // Add search filter
    if (search) {
      query.$or = [
        { 'transcriptions.text': { $regex: search, $options: 'i' } },
        { subject: { $regex: search, $options: 'i' } },
        { senderEmail: { $regex: search, $options: 'i' } }
      ];
    }
    
    // Add sentiment filter
    if (sentiment) {
      query['transcriptions.aiAnalysis.sentiment'] = sentiment;
    }
    
    // Add urgency filter
    if (urgency) {
      query['transcriptions.aiAnalysis.urgencyLevel'] = { $gte: parseInt(urgency) };
    }
    
    // Add date range filter
    if (dateFrom || dateTo) {
      query.receivedAt = {};
      if (dateFrom) query.receivedAt.$gte = new Date(dateFrom);
      if (dateTo) query.receivedAt.$lte = new Date(dateTo);
    }
    
    // Execute query with pagination
    const emails = await Email.find(query)
      .sort({ receivedAt: -1 })
      .skip((page - 1) * limit)
      .limit(parseInt(limit))
      .select('subject senderEmail receivedAt transcriptions aiAnalysis');
    
    const total = await Email.countDocuments(query);
    
    // Flatten transcriptions for easier frontend consumption
    const transcriptions = [];
    emails.forEach(email => {
      email.transcriptions.forEach(transcription => {
        transcriptions.push({
          emailId: email._id,
          emailSubject: email.subject,
          senderEmail: email.senderEmail,
          receivedAt: email.receivedAt,
          ...transcription.toObject()
        });
      });
    });
    
    res.status(200).json({
      success: true,
      data: {
        transcriptions,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
    
  } catch (error) {
    console.error('❌ Error getting transcriptions:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get transcriptions',
      error: error.message
    });
  }
};

/**
 * MANUAL EMAIL PROCESSING
 * Manually trigger processing for specific email
 */
const processSpecificEmail = async (req, res) => {
  try {
    const { emailId } = req.params;
    
    const email = await Email.findById(emailId);
    if (!email) {
      return res.status(404).json({
        success: false,
        message: 'Email not found'
      });
    }
    
    // Process transcriptions if M4A files exist
    let transcriptionResult = null;
    if (email.attachments.some(att => att.isM4A && !att.transcribed)) {
      transcriptionResult = await transcriptionService.processEmailTranscriptions(email);
    }
    
    // Re-run AI analysis
    const updatedEmail = await Email.findById(emailId);
    
    res.status(200).json({
      success: true,
      message: 'Email processed successfully',
      data: {
        email: updatedEmail,
        transcriptionResult
      }
    });
    
  } catch (error) {
    console.error('❌ Error processing specific email:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process email',
      error: error.message
    });
  }
};

/**
 * GET EMAIL ANALYTICS
 * Returns analytics and insights about email processing
 */
const getEmailAnalytics = async (req, res) => {
  try {
    const { period = '30' } = req.query; // days
    const startDate = new Date(Date.now() - parseInt(period) * 24 * 60 * 60 * 1000);
    
    // Email volume analytics
    const emailVolume = await Email.aggregate([
      { $match: { receivedAt: { $gte: startDate } } },
      {
        $group: {
          _id: { $dateToString: { format: '%Y-%m-%d', date: '$receivedAt' } },
          count: { $sum: 1 },
          m4aCount: { $sum: { $size: { $filter: { input: '$attachments', cond: { $eq: ['$$this.isM4A', true] } } } } },
          invoiceCount: { $sum: { $cond: [{ $eq: ['$invoiceAnalysis.analyzed', true] }, 1, 0] } }
        }
      },
      { $sort: { _id: 1 } }
    ]);
    
    // Sentiment distribution
    const sentimentDistribution = await Email.aggregate([
      { $match: { receivedAt: { $gte: startDate } } },
      { $unwind: '$transcriptions' },
      {
        $group: {
          _id: '$transcriptions.aiAnalysis.sentiment',
          count: { $sum: 1 }
        }
      }
    ]);
    
    // Top senders
    const topSenders = await Email.aggregate([
      { $match: { receivedAt: { $gte: startDate } } },
      {
        $group: {
          _id: '$senderEmail',
          count: { $sum: 1 },
          lastEmail: { $max: '$receivedAt' }
        }
      },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);
    
    // Service request trends
    const serviceRequests = await Email.aggregate([
      { $match: { receivedAt: { $gte: startDate } } },
      { $unwind: '$transcriptions' },
      { $match: { 'transcriptions.aiAnalysis.isServiceRequest': true } },
      {
        $group: {
          _id: { $dateToString: { format: '%Y-%m-%d', date: '$receivedAt' } },
          count: { $sum: 1 },
          avgUrgency: { $avg: '$transcriptions.aiAnalysis.urgencyLevel' }
        }
      },
      { $sort: { _id: 1 } }
    ]);
    
    res.status(200).json({
      success: true,
      data: {
        emailVolume,
        sentimentDistribution,
        topSenders,
        serviceRequests,
        period: `${period} days`,
        generatedAt: new Date()
      }
    });
    
  } catch (error) {
    console.error('❌ Error getting email analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get email analytics',
      error: error.message
    });
  }
};

module.exports = {
  processAllEmails,
  processAllTranscriptions,
  getProcessingStatus,
  getEmailDetails,
  getTranscriptions,
  processSpecificEmail,
  getEmailAnalytics
};
