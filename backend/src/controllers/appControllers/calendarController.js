/**
 * CALENDAR CONTROLLER
 * API endpoints for enhanced calendar integration with 3-category HVAC system
 * Categories: 🔧 <PERSON><PERSON><PERSON>, 🏗️ Instal<PERSON>ja, 🔍 Oględziny
 */

const { validationResult } = require('express-validator');
const moment = require('moment');

// Import services
const calendarService = require('@/services/calendarService');

// Import models
const ServiceOrder = require('@/models/appModels/ServiceOrder');

/**
 * MAIN CALENDAR ENDPOINTS
 */

// Get calendar events with filtering and optimization
const getCalendarEvents = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const { 
      startDate, 
      endDate, 
      category = 'all',
      district = 'all',
      technician = 'all',
      status = 'all',
      optimizeRoutes = false,
      optimizeDate = null
    } = req.query;

    console.log(`📅 Loading calendar events from ${startDate} to ${endDate}`);

    const filters = {
      category,
      district,
      technician,
      status,
      optimizeRoutes: optimizeRoutes === 'true',
      optimizeDate
    };

    const events = await calendarService.getCalendarEvents(startDate, endDate, filters);

    res.status(200).json({
      success: true,
      result: {
        events,
        filters,
        totalEvents: events.length
      },
      message: 'Calendar events loaded successfully'
    });

  } catch (error) {
    console.error(`❌ Failed to load calendar events: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'Failed to load calendar events',
      error: error.message
    });
  }
};

// Create new calendar event
const createCalendarEvent = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const eventData = {
      ...req.body,
      createdBy: req.user?.id || 'system'
    };

    console.log(`📅 Creating new calendar event: ${eventData.title}`);

    const calendarEvent = await calendarService.createCalendarEvent(eventData);

    res.status(201).json({
      success: true,
      result: calendarEvent,
      message: 'Calendar event created successfully'
    });

  } catch (error) {
    console.error(`❌ Failed to create calendar event: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'Failed to create calendar event',
      error: error.message
    });
  }
};

// Update calendar event
const updateCalendarEvent = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const { eventId } = req.params;
    const updateData = {
      ...req.body,
      updatedBy: req.user?.id || 'system'
    };

    console.log(`📅 Updating calendar event: ${eventId}`);

    const calendarEvent = await calendarService.updateCalendarEvent(eventId, updateData);

    res.status(200).json({
      success: true,
      result: calendarEvent,
      message: 'Calendar event updated successfully'
    });

  } catch (error) {
    console.error(`❌ Failed to update calendar event: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'Failed to update calendar event',
      error: error.message
    });
  }
};

// Delete calendar event
const deleteCalendarEvent = async (req, res) => {
  try {
    const { eventId } = req.params;

    console.log(`📅 Deleting calendar event: ${eventId}`);

    const result = await calendarService.deleteCalendarEvent(eventId);

    res.status(200).json({
      success: true,
      result,
      message: 'Calendar event deleted successfully'
    });

  } catch (error) {
    console.error(`❌ Failed to delete calendar event: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'Failed to delete calendar event',
      error: error.message
    });
  }
};

/**
 * CALENDAR ANALYTICS AND OPTIMIZATION ENDPOINTS
 */

// Get calendar dashboard data
const getCalendarDashboard = async (req, res) => {
  try {
    const { 
      startDate = moment().startOf('month').format('YYYY-MM-DD'),
      endDate = moment().endOf('month').format('YYYY-MM-DD')
    } = req.query;

    console.log(`📊 Loading calendar dashboard for ${startDate} to ${endDate}`);

    const [statistics, events] = await Promise.all([
      calendarService.getCalendarStatistics(startDate, endDate),
      calendarService.getCalendarEvents(startDate, endDate)
    ]);

    // Calculate additional metrics
    const dashboardData = {
      statistics,
      metrics: {
        totalEvents: statistics.totalEvents,
        eventsThisWeek: events.filter(event => 
          moment(event.start).isBetween(moment().startOf('week'), moment().endOf('week'))
        ).length,
        eventsToday: events.filter(event => 
          moment(event.start).isSame(moment(), 'day')
        ).length,
        upcomingEvents: events.filter(event => 
          moment(event.start).isAfter(moment())
        ).length
      },
      categoryBreakdown: Object.entries(calendarService.HVAC_CATEGORIES).map(([key, config]) => ({
        category: key,
        name: config.name,
        icon: config.icon,
        color: config.color,
        count: statistics.eventsByCategory[key] || 0
      })),
      districtBreakdown: Object.entries(statistics.eventsByDistrict).map(([district, count]) => ({
        district,
        count,
        zone: calendarService.WARSAW_DISTRICTS[district]?.zone || 'unknown'
      }))
    };

    res.status(200).json({
      success: true,
      result: dashboardData,
      message: 'Calendar dashboard data loaded successfully'
    });

  } catch (error) {
    console.error(`❌ Failed to load calendar dashboard: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'Failed to load calendar dashboard',
      error: error.message
    });
  }
};

// Get Warsaw route optimization
const getWarsawRouteOptimization = async (req, res) => {
  try {
    const { date } = req.query;

    if (!date) {
      return res.status(400).json({
        success: false,
        message: 'Date parameter is required'
      });
    }

    console.log(`🗺️ Optimizing Warsaw routes for ${date}`);

    const startDate = moment(date).startOf('day').format('YYYY-MM-DD');
    const endDate = moment(date).endOf('day').format('YYYY-MM-DD');

    const events = await calendarService.getCalendarEvents(startDate, endDate);
    const optimizedEvents = await calendarService.optimizeWarsawRoutes(events, date);

    // Group by zone for better visualization
    const routesByZone = {};
    optimizedEvents.forEach(event => {
      const district = event.location?.district;
      const zone = calendarService.WARSAW_DISTRICTS[district]?.zone || 'unknown';
      
      if (!routesByZone[zone]) {
        routesByZone[zone] = [];
      }
      routesByZone[zone].push(event);
    });

    res.status(200).json({
      success: true,
      result: {
        date,
        totalEvents: optimizedEvents.length,
        routesByZone,
        optimizedEvents,
        zones: Object.keys(routesByZone)
      },
      message: 'Warsaw route optimization completed'
    });

  } catch (error) {
    console.error(`❌ Route optimization failed: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'Route optimization failed',
      error: error.message
    });
  }
};

// Get technician availability
const getTechnicianAvailability = async (req, res) => {
  try {
    const { technicianId, date } = req.query;

    if (!technicianId || !date) {
      return res.status(400).json({
        success: false,
        message: 'Technician ID and date are required'
      });
    }

    console.log(`👨‍🔧 Checking availability for technician ${technicianId} on ${date}`);

    const availability = await calendarService.getTechnicianAvailability(technicianId, date);

    res.status(200).json({
      success: true,
      result: availability,
      message: 'Technician availability retrieved successfully'
    });

  } catch (error) {
    console.error(`❌ Failed to get technician availability: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'Failed to get technician availability',
      error: error.message
    });
  }
};

// Suggest optimal time slots
const suggestOptimalTimeSlots = async (req, res) => {
  try {
    const { district, category, preferredDate } = req.query;

    if (!district || !category || !preferredDate) {
      return res.status(400).json({
        success: false,
        message: 'District, category, and preferred date are required'
      });
    }

    console.log(`💡 Suggesting optimal time slots for ${category} in ${district} around ${preferredDate}`);

    const suggestions = await calendarService.suggestOptimalTimeSlots(district, category, preferredDate);

    res.status(200).json({
      success: true,
      result: {
        district,
        category,
        preferredDate,
        suggestions,
        totalSuggestions: suggestions.length
      },
      message: 'Optimal time slots suggested successfully'
    });

  } catch (error) {
    console.error(`❌ Failed to suggest time slots: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'Failed to suggest time slots',
      error: error.message
    });
  }
};

/**
 * CONFIGURATION ENDPOINTS
 */

// Get HVAC categories configuration
const getHVACCategories = async (req, res) => {
  try {
    const categories = Object.entries(calendarService.HVAC_CATEGORIES).map(([key, config]) => ({
      key,
      ...config
    }));

    res.status(200).json({
      success: true,
      result: categories,
      message: 'HVAC categories retrieved successfully'
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve HVAC categories',
      error: error.message
    });
  }
};

// Get Warsaw districts configuration
const getWarsawDistricts = async (req, res) => {
  try {
    const districts = Object.entries(calendarService.WARSAW_DISTRICTS).map(([name, config]) => ({
      name,
      ...config
    }));

    res.status(200).json({
      success: true,
      result: districts,
      message: 'Warsaw districts retrieved successfully'
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve Warsaw districts',
      error: error.message
    });
  }
};

module.exports = {
  getCalendarEvents,
  createCalendarEvent,
  updateCalendarEvent,
  deleteCalendarEvent,
  getCalendarDashboard,
  getWarsawRouteOptimization,
  getTechnicianAvailability,
  suggestOptimalTimeSlots,
  getHVACCategories,
  getWarsawDistricts
};
