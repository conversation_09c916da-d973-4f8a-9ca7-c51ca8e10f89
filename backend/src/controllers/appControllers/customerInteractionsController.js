/**
 * CUSTOMER INTERACTIONS CONTROLLER
 * API endpoints for unified customer interactions data panel
 * Combines emails + transcriptions + communication history
 */

const { validationResult } = require('express-validator');

// Import services
const customerInteractionsService = require('@/services/customerInteractionsService');

// Import models
const Client = require('@/models/appModels/Client');

/**
 * MAIN CUSTOMER INTERACTIONS ENDPOINTS
 */

// Get comprehensive customer interactions data
const getCustomerInteractions = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    const { customerId } = req.params;

    console.log(`📊 Loading customer interactions for ${customerId}`);

    const result = await customerInteractionsService.getCustomerInteractions(customerId);

    res.status(200).json({
      success: true,
      result,
      message: 'Customer interactions loaded successfully'
    });

  } catch (error) {
    console.error(`❌ Failed to load customer interactions: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'Failed to load customer interactions',
      error: error.message
    });
  }
};

// Get customer interactions summary for multiple customers
const getCustomerInteractionsSummary = async (req, res) => {
  try {
    const { customerIds, limit = 50 } = req.query;

    let ids = [];
    if (customerIds) {
      ids = Array.isArray(customerIds) ? customerIds : customerIds.split(',');
    } else {
      // Get recent customers if no IDs provided
      const recentCustomers = await Client.find({})
        .sort({ updatedAt: -1 })
        .limit(parseInt(limit))
        .select('_id');
      ids = recentCustomers.map(c => c._id.toString());
    }

    console.log(`📊 Loading interactions summary for ${ids.length} customers`);

    const result = await customerInteractionsService.getCustomerInteractionsSummary(ids);

    res.status(200).json({
      success: true,
      result,
      message: 'Customer interactions summary loaded successfully'
    });

  } catch (error) {
    console.error(`❌ Failed to load interactions summary: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'Failed to load interactions summary',
      error: error.message
    });
  }
};

// Get customer interactions timeline with filtering
const getCustomerTimeline = async (req, res) => {
  try {
    const { customerId } = req.params;
    const { 
      type, 
      limit = 50, 
      offset = 0,
      dateFrom,
      dateTo 
    } = req.query;

    const interactions = await customerInteractionsService.getCustomerInteractions(customerId);
    
    let timeline = interactions.timeline || [];

    // Apply filters
    if (type && type !== 'all') {
      timeline = timeline.filter(item => item.type === type);
    }

    if (dateFrom) {
      timeline = timeline.filter(item => new Date(item.timestamp) >= new Date(dateFrom));
    }

    if (dateTo) {
      timeline = timeline.filter(item => new Date(item.timestamp) <= new Date(dateTo));
    }

    // Apply pagination
    const total = timeline.length;
    timeline = timeline.slice(parseInt(offset), parseInt(offset) + parseInt(limit));

    res.status(200).json({
      success: true,
      result: {
        timeline,
        pagination: {
          total,
          limit: parseInt(limit),
          offset: parseInt(offset),
          hasMore: parseInt(offset) + parseInt(limit) < total
        }
      },
      message: 'Customer timeline loaded successfully'
    });

  } catch (error) {
    console.error(`❌ Failed to load customer timeline: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'Failed to load customer timeline',
      error: error.message
    });
  }
};

// Get customer interaction analytics
const getCustomerAnalytics = async (req, res) => {
  try {
    const { customerId } = req.params;

    const interactions = await customerInteractionsService.getCustomerInteractions(customerId);

    const analytics = {
      healthScore: interactions.customer.healthScore,
      metrics: interactions.metrics,
      aiInsights: interactions.aiInsights,
      summary: interactions.summary,
      trends: {
        communicationFrequency: calculateCommunicationTrend(interactions.timeline),
        responseTime: calculateResponseTimeTrend(interactions.timeline),
        issueResolution: calculateIssueResolutionTrend(interactions.timeline)
      }
    };

    res.status(200).json({
      success: true,
      result: analytics,
      message: 'Customer analytics loaded successfully'
    });

  } catch (error) {
    console.error(`❌ Failed to load customer analytics: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'Failed to load customer analytics',
      error: error.message
    });
  }
};

/**
 * CUSTOMER HEALTH AND INSIGHTS ENDPOINTS
 */

// Get customer health dashboard
const getCustomerHealthDashboard = async (req, res) => {
  try {
    const { limit = 20 } = req.query;

    // Get customers with recent activity
    const recentCustomers = await Client.find({})
      .sort({ updatedAt: -1 })
      .limit(parseInt(limit))
      .select('_id name email');

    const customerIds = recentCustomers.map(c => c._id.toString());
    const summaries = await customerInteractionsService.getCustomerInteractionsSummary(customerIds);

    // Sort by health score
    const healthDashboard = summaries
      .sort((a, b) => (b.healthScore || 0) - (a.healthScore || 0))
      .map(summary => ({
        ...summary,
        healthStatus: getHealthStatus(summary.healthScore),
        riskLevel: getRiskLevel(summary.healthScore)
      }));

    // Calculate overall statistics
    const stats = {
      totalCustomers: healthDashboard.length,
      averageHealthScore: healthDashboard.reduce((sum, c) => sum + (c.healthScore || 0), 0) / healthDashboard.length,
      healthyCustomers: healthDashboard.filter(c => c.healthScore >= 70).length,
      atRiskCustomers: healthDashboard.filter(c => c.healthScore < 50).length,
      activeCustomers: healthDashboard.filter(c => c.totalInteractions > 0).length
    };

    res.status(200).json({
      success: true,
      result: {
        customers: healthDashboard,
        statistics: stats
      },
      message: 'Customer health dashboard loaded successfully'
    });

  } catch (error) {
    console.error(`❌ Failed to load health dashboard: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'Failed to load health dashboard',
      error: error.message
    });
  }
};

// Update customer health score manually
const updateCustomerHealthScore = async (req, res) => {
  try {
    const { customerId } = req.params;
    const { healthScore, notes } = req.body;

    if (healthScore < 0 || healthScore > 100) {
      return res.status(400).json({
        success: false,
        message: 'Health score must be between 0 and 100'
      });
    }

    const customer = await Client.findByIdAndUpdate(
      customerId,
      {
        healthScore,
        healthScoreNotes: notes,
        healthScoreUpdatedAt: new Date(),
        healthScoreUpdatedBy: req.user?.id || 'system'
      },
      { new: true }
    );

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found'
      });
    }

    res.status(200).json({
      success: true,
      result: {
        customerId,
        healthScore,
        notes,
        updatedAt: new Date()
      },
      message: 'Customer health score updated successfully'
    });

  } catch (error) {
    console.error(`❌ Failed to update health score: ${error.message}`);
    res.status(500).json({
      success: false,
      message: 'Failed to update health score',
      error: error.message
    });
  }
};

/**
 * HELPER FUNCTIONS
 */

// Calculate communication trend
const calculateCommunicationTrend = (timeline) => {
  const last30Days = timeline.filter(item => {
    const itemDate = new Date(item.timestamp);
    const cutoff = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    return itemDate >= cutoff;
  });

  const weeklyData = [];
  for (let i = 0; i < 4; i++) {
    const weekStart = new Date(Date.now() - (i + 1) * 7 * 24 * 60 * 60 * 1000);
    const weekEnd = new Date(Date.now() - i * 7 * 24 * 60 * 60 * 1000);
    
    const weekCount = last30Days.filter(item => {
      const itemDate = new Date(item.timestamp);
      return itemDate >= weekStart && itemDate < weekEnd;
    }).length;

    weeklyData.unshift({ week: i + 1, count: weekCount });
  }

  return weeklyData;
};

// Calculate response time trend
const calculateResponseTimeTrend = (timeline) => {
  // Simplified - would need more complex logic to track actual response times
  return {
    average: 24, // hours
    trend: 'improving',
    lastResponse: timeline[0]?.timestamp || null
  };
};

// Calculate issue resolution trend
const calculateIssueResolutionTrend = (timeline) => {
  const serviceItems = timeline.filter(item => item.type === 'service');
  const resolvedItems = serviceItems.filter(item => 
    item.details?.status === 'completed' || item.details?.status === 'closed'
  );

  return {
    totalIssues: serviceItems.length,
    resolvedIssues: resolvedItems.length,
    resolutionRate: serviceItems.length > 0 ? (resolvedItems.length / serviceItems.length) * 100 : 0,
    averageResolutionTime: 48 // hours - simplified
  };
};

// Get health status label
const getHealthStatus = (score) => {
  if (score >= 80) return 'excellent';
  if (score >= 70) return 'good';
  if (score >= 50) return 'fair';
  if (score >= 30) return 'poor';
  return 'critical';
};

// Get risk level
const getRiskLevel = (score) => {
  if (score >= 70) return 'low';
  if (score >= 50) return 'medium';
  return 'high';
};

module.exports = {
  getCustomerInteractions,
  getCustomerInteractionsSummary,
  getCustomerTimeline,
  getCustomerAnalytics,
  getCustomerHealthDashboard,
  updateCustomerHealthScore
};
