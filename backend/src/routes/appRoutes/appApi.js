const express = require('express');
const { catchErrors } = require('@/handlers/errorHandlers');
const router = express.Router();

const appControllers = require('@/controllers/appControllers');
const { routesList } = require('@/models/utils');
const semanticMiddleware = require('@/middleware/semantic-middleware');

const routerApp = (entity, controller) => {
  // Apply semantic middleware to key entities
  const useSemanticMiddleware = ['client', 'serviceorder', 'opportunity'].includes(entity);

  if (useSemanticMiddleware) {
    // Routes with semantic processing
    if (entity === 'client') {
      router.route(`/${entity}/create`).post(semanticMiddleware.clientSemanticProcessor(), catchErrors(controller['create']));
      router.route(`/${entity}/update/:id`).patch(semanticMiddleware.clientSemanticProcessor(), catchErrors(controller['update']));
    } else if (entity === 'serviceorder') {
      router.route(`/${entity}/create`).post(semanticMiddleware.serviceOrderSemanticProcessor(), catchErrors(controller['create']));
      router.route(`/${entity}/update/:id`).patch(semanticMiddleware.serviceOrderSemanticProcessor(), catchErrors(controller['update']));
    } else if (entity === 'opportunity') {
      router.route(`/${entity}/create`).post(semanticMiddleware.opportunitySemanticProcessor(), catchErrors(controller['create']));
      router.route(`/${entity}/update/:id`).patch(semanticMiddleware.opportunitySemanticProcessor(), catchErrors(controller['update']));
    }

    // Standard routes without semantic processing for these entities
    router.route(`/${entity}/read/:id`).get(catchErrors(controller['read']));
    router.route(`/${entity}/delete/:id`).delete(catchErrors(controller['delete']));
  } else {
    // Standard routes for other entities
    router.route(`/${entity}/create`).post(catchErrors(controller['create']));
    router.route(`/${entity}/read/:id`).get(catchErrors(controller['read']));
    router.route(`/${entity}/update/:id`).patch(catchErrors(controller['update']));
    router.route(`/${entity}/delete/:id`).delete(catchErrors(controller['delete']));
  }

  // Common routes for all entities
  router.route(`/${entity}/search`).get(catchErrors(controller['search']));
  router.route(`/${entity}/list`).get(catchErrors(controller['list']));
  router.route(`/${entity}/listAll`).get(catchErrors(controller['listAll']));
  router.route(`/${entity}/filter`).get(catchErrors(controller['filter']));
  router.route(`/${entity}/summary`).get(catchErrors(controller['summary']));

  if (entity === 'invoice' || entity === 'quote' || entity === 'payment') {
    router.route(`/${entity}/mail`).post(catchErrors(controller['mail']));
  }

  if (entity === 'quote') {
    router.route(`/${entity}/convert/:id`).get(catchErrors(controller['convert']));
  }

  // HVAC-specific routes
  if (entity === 'equipment') {
    router.route(`/${entity}/client/:clientId`).get(catchErrors(controller['getByClient']));
    router.route(`/${entity}/health/:id`).patch(catchErrors(controller['updateHealthScore']));
  }

  if (entity === 'serviceorder') {
    router.route(`/${entity}/technician/:technicianId`).get(catchErrors(controller['getByTechnician']));
    router.route(`/${entity}/stage/:id`).patch(catchErrors(controller['updateStage']));
    router.route(`/${entity}/kanban`).get(catchErrors(controller['getKanbanBoard']));
    router.route(`/${entity}/assignment-recommendations`).post(catchErrors(controller['getAssignmentRecommendations']));
  }

  if (entity === 'opportunity') {
    router.route(`/${entity}/pipeline`).get(catchErrors(controller['getSalesPipeline']));
    router.route(`/${entity}/stage/:id`).patch(catchErrors(controller['updateStage']));
  }
};

routesList.forEach(({ entity, controllerName }) => {
  const controller = appControllers[controllerName];
  routerApp(entity, controller);
});

// Semantic Intelligence endpoints
router.route('/semantic/insights/:customerId').get(catchErrors(async (req, res) => {
  const insights = await semanticMiddleware.getCustomerSemanticInsights(req.params.customerId);
  res.json({ success: true, result: insights });
}));

router.route('/semantic/health').get(catchErrors(async (req, res) => {
  const health = await semanticMiddleware.healthCheck();
  res.json({ success: true, result: health });
}));

module.exports = router;
