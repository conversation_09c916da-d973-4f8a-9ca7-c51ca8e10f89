const express = require('express');
const router = express.Router();
const { body, param, query } = require('express-validator');

const emailProcessingController = require('../../controllers/appControllers/emailProcessingController');

/**
 * EMAIL PROCESSING ROUTES
 * Handles email processing, transcriptions, and AI analysis
 */

/**
 * @route   POST /api/email-processing/process-all
 * @desc    Process all emails from both accounts (dolores@ and grz<PERSON><PERSON>@)
 * @access  Private (Admin)
 */
router.post('/process-all', emailProcessingController.processAllEmails);

/**
 * @route   POST /api/email-processing/transcribe-all
 * @desc    Process all M4A transcriptions from emails
 * @access  Private (Admin)
 */
router.post('/transcribe-all', emailProcessingController.processAllTranscriptions);

/**
 * @route   GET /api/email-processing/status
 * @desc    Get current email processing status and statistics
 * @access  Private (Admin)
 */
router.get('/status', emailProcessingController.getProcessingStatus);

/**
 * @route   GET /api/email-processing/analytics
 * @desc    Get email processing analytics and insights
 * @access  Private (Admin)
 * @query   period - Number of days for analytics (default: 30)
 */
router.get('/analytics', [
  query('period')
    .optional()
    .isInt({ min: 1, max: 365 })
    .withMessage('Period must be between 1 and 365 days')
], emailProcessingController.getEmailAnalytics);

/**
 * @route   GET /api/email-processing/emails/:emailId
 * @desc    Get detailed information about specific email
 * @access  Private (Admin)
 */
router.get('/emails/:emailId', [
  param('emailId')
    .isMongoId()
    .withMessage('Invalid email ID')
], emailProcessingController.getEmailDetails);

/**
 * @route   POST /api/email-processing/emails/:emailId/process
 * @desc    Manually process specific email (transcriptions, AI analysis)
 * @access  Private (Admin)
 */
router.post('/emails/:emailId/process', [
  param('emailId')
    .isMongoId()
    .withMessage('Invalid email ID')
], emailProcessingController.processSpecificEmail);

/**
 * @route   GET /api/email-processing/transcriptions
 * @desc    Get all transcriptions with filtering and pagination
 * @access  Private (Admin)
 * @query   page - Page number (default: 1)
 * @query   limit - Items per page (default: 20)
 * @query   search - Search in transcription text
 * @query   sentiment - Filter by sentiment (positive/negative/neutral)
 * @query   urgency - Filter by minimum urgency level (1-10)
 * @query   dateFrom - Filter from date (YYYY-MM-DD)
 * @query   dateTo - Filter to date (YYYY-MM-DD)
 */
router.get('/transcriptions', [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('sentiment')
    .optional()
    .isIn(['positive', 'negative', 'neutral'])
    .withMessage('Sentiment must be positive, negative, or neutral'),
  query('urgency')
    .optional()
    .isInt({ min: 1, max: 10 })
    .withMessage('Urgency must be between 1 and 10'),
  query('dateFrom')
    .optional()
    .isISO8601()
    .withMessage('Date from must be valid ISO date'),
  query('dateTo')
    .optional()
    .isISO8601()
    .withMessage('Date to must be valid ISO date')
], emailProcessingController.getTranscriptions);

module.exports = router;
