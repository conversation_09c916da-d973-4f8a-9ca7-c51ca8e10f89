const fs = require('fs').promises;
const path = require('path');
const FormData = require('form-data');
const fetch = require('node-fetch');
const Email = require('../models/appModels/Email');
const ServiceOrder = require('../models/appModels/ServiceOrder');
const Opportunity = require('../models/appModels/Opportunity');

class TranscriptionService {
  constructor() {
    this.nemoDemoUrl = 'https://api.nvidia.com/v1/audio/transcriptions'; // Demo endpoint
    this.elevenLabsUrl = 'https://api.elevenlabs.io/v1/speech-to-text';
    this.tempDir = path.join(__dirname, '../../temp');
    this.ensureTempDir();
  }

  async ensureTempDir() {
    try {
      await fs.mkdir(this.tempDir, { recursive: true });
    } catch (error) {
      console.error('Error creating temp directory:', error);
    }
  }

  /**
   * MAIN TRANSCRIPTION PIPELINE
   * Processes all M4A files from emails and creates transcriptions
   */
  async processAllTranscriptions() {
    try {
      console.log('🎵 Starting transcription processing pipeline...');
      
      // Find emails with M4A attachments that haven't been transcribed
      const emailsWithM4A = await Email.find({
        'attachments.isM4A': true,
        'attachments.transcribed': { $ne: true }
      });
      
      console.log(`📧 Found ${emailsWithM4A.length} emails with untranscribed M4A files`);
      
      let totalTranscribed = 0;
      let totalFailed = 0;
      
      for (const email of emailsWithM4A) {
        try {
          const result = await this.processEmailTranscriptions(email);
          totalTranscribed += result.transcribed;
          totalFailed += result.failed;
        } catch (error) {
          console.error(`❌ Error processing email ${email._id}:`, error);
          totalFailed++;
        }
      }
      
      console.log(`✅ Transcription processing completed!`);
      console.log(`🎵 Total transcribed: ${totalTranscribed}`);
      console.log(`❌ Total failed: ${totalFailed}`);
      
      return {
        success: true,
        totalTranscribed,
        totalFailed,
        timestamp: new Date()
      };
      
    } catch (error) {
      console.error('❌ Transcription pipeline error:', error);
      throw error;
    }
  }

  /**
   * PROCESS TRANSCRIPTIONS FOR SINGLE EMAIL
   * Transcribes all M4A attachments in an email
   */
  async processEmailTranscriptions(email) {
    let transcribed = 0;
    let failed = 0;
    
    for (let i = 0; i < email.attachments.length; i++) {
      const attachment = email.attachments[i];
      
      if (attachment.isM4A && !attachment.transcribed) {
        try {
          console.log(`🎵 Transcribing: ${attachment.filename}`);
          
          // Save attachment to temp file
          const tempFilePath = await this.saveAttachmentToTemp(attachment);
          
          // Transcribe with primary service (NVIDIA NeMo)
          let transcriptionResult = await this.transcribeWithNemo(tempFilePath);
          
          // Fallback to ElevenLabs if NeMo fails
          if (!transcriptionResult.success) {
            console.log('🔄 Falling back to ElevenLabs...');
            transcriptionResult = await this.transcribeWithElevenLabs(tempFilePath);
          }
          
          if (transcriptionResult.success) {
            // Update attachment
            email.attachments[i].transcribed = true;
            email.attachments[i].transcriptionText = transcriptionResult.text;
            email.attachments[i].transcriptionConfidence = transcriptionResult.confidence;
            
            // Add to transcriptions array
            const transcriptionData = {
              filename: attachment.filename,
              text: transcriptionResult.text,
              confidence: transcriptionResult.confidence,
              language: 'pl',
              duration: transcriptionResult.duration || 0,
              processedAt: new Date()
            };
            
            // AI analysis of transcription
            const aiAnalysis = await this.analyzeTranscription(transcriptionResult.text);
            transcriptionData.aiAnalysis = aiAnalysis;
            
            email.transcriptions.push(transcriptionData);
            
            // Create service order or opportunity if needed
            await this.createTasksFromTranscription(email, transcriptionData);
            
            transcribed++;
            console.log(`✅ Transcribed: ${attachment.filename}`);
          } else {
            failed++;
            console.log(`❌ Failed to transcribe: ${attachment.filename}`);
          }
          
          // Clean up temp file
          await this.cleanupTempFile(tempFilePath);
          
        } catch (error) {
          console.error(`❌ Error transcribing ${attachment.filename}:`, error);
          failed++;
        }
      }
    }
    
    // Save updated email
    if (transcribed > 0) {
      await email.save();
    }
    
    return { transcribed, failed };
  }

  /**
   * TRANSCRIBE WITH NVIDIA NEMO (PRIMARY)
   * Uses NVIDIA NeMo FastConformer for Polish transcription
   */
  async transcribeWithNemo(filePath) {
    try {
      // For demo purposes, we'll use a mock implementation
      // In production, this would connect to actual NVIDIA NeMo service
      
      const formData = new FormData();
      formData.append('audio', await fs.readFile(filePath), {
        filename: path.basename(filePath),
        contentType: 'audio/m4a'
      });
      formData.append('language', 'pl');
      formData.append('model', 'fastconformer');
      
      // Mock response for demo
      const mockTranscription = await this.mockPolishTranscription(filePath);
      
      return {
        success: true,
        text: mockTranscription,
        confidence: 0.92,
        duration: 120, // seconds
        service: 'nvidia_nemo'
      };
      
    } catch (error) {
      console.error('❌ NVIDIA NeMo transcription error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * TRANSCRIBE WITH ELEVENLABS (FALLBACK)
   * Uses ElevenLabs Scribe as backup transcription service
   */
  async transcribeWithElevenLabs(filePath) {
    try {
      // Mock implementation for demo
      const mockTranscription = await this.mockPolishTranscription(filePath);
      
      return {
        success: true,
        text: mockTranscription,
        confidence: 0.88,
        duration: 120,
        service: 'elevenlabs_scribe'
      };
      
    } catch (error) {
      console.error('❌ ElevenLabs transcription error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * MOCK POLISH TRANSCRIPTION
   * Generates realistic Polish HVAC transcriptions for demo
   */
  async mockPolishTranscription(filePath) {
    const mockTranscriptions = [
      "Dzień dobry, dzwonię w sprawie klimatyzacji w biurze. Mamy problem z jednostką Daikin, nie chłodzi jak powinna. Czy mogliby Państwo przyjechać na przegląd? Adres to Warszawa, Mokotów, ulica Puławska 15. Najlepiej w przyszłym tygodniu, bo jest bardzo gorąco.",
      
      "Witam, jestem właścicielem restauracji na Starówce. Potrzebujemy pilnej naprawy wentylacji w kuchni. System LG przestał działać wczoraj wieczorem. To bardzo pilne, bo nie możemy gotować bez sprawnej wentylacji. Proszę o szybki kontakt.",
      
      "Dzwonię w sprawie oferty na instalację pompy ciepła w domu jednorodzinnym. Dom ma 150 metrów kwadratowych, położony w Wilanowie. Interesuje nas rozwiązanie Mitsubishi lub podobne. Proszę o przygotowanie wyceny i umówienie spotkania.",
      
      "Mam problem z klimatyzacją Samsung w salonie. Urządzenie włącza się, ale nie chłodzi. Kupione rok temu, może być jeszcze na gwarancji. Mieszkam na Żoliborzu, czy mogliby Państwo sprawdzić co się dzieje?",
      
      "Witam, reprezentuję firmę budowlaną. Potrzebujemy kompleksowej instalacji HVAC w nowym biurowcu na Woli. 5 pięter, około 2000 metrów kwadratowych. Szukamy partnera do długoterminowej współpracy. Proszę o kontakt w sprawie szczegółów."
    ];
    
    return mockTranscriptions[Math.floor(Math.random() * mockTranscriptions.length)];
  }

  /**
   * AI ANALYSIS OF TRANSCRIPTION
   * Analyzes transcription content for HVAC insights
   */
  async analyzeTranscription(text) {
    try {
      // Use LM Studio for local AI analysis
      const response = await fetch(`${process.env.LM_STUDIO_URL}/v1/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: process.env.LM_STUDIO_MODEL,
          messages: [
            {
              role: 'system',
              content: 'Jesteś ekspertem analizy transkrypcji HVAC. Analizuj treść pod kątem: sentymentu, intencji, pilności, typu usługi.'
            },
            {
              role: 'user',
              content: `Przeanalizuj tę transkrypcję rozmowy HVAC:
              
              "${text}"
              
              Określ: sentiment, intent, urgencyLevel (1-10), isServiceRequest (true/false), keywords (lista)`
            }
          ],
          temperature: 0.3,
          max_tokens: 300
        })
      });
      
      const result = await response.json();
      const analysis = result.choices[0]?.message?.content;
      
      return {
        sentiment: this.extractSentiment(analysis),
        intent: this.extractIntent(analysis),
        keywords: this.extractKeywords(text),
        isServiceRequest: this.isServiceRequest(text),
        urgencyLevel: this.extractUrgency(analysis)
      };
      
    } catch (error) {
      console.error('❌ Transcription AI analysis error:', error);
      return {
        sentiment: 'neutral',
        intent: 'unknown',
        keywords: [],
        isServiceRequest: false,
        urgencyLevel: 5
      };
    }
  }

  /**
   * CREATE TASKS FROM TRANSCRIPTION
   * Automatically creates service orders or opportunities based on transcription content
   */
  async createTasksFromTranscription(email, transcription) {
    try {
      const analysis = transcription.aiAnalysis;
      
      if (analysis.isServiceRequest && analysis.urgencyLevel >= 7) {
        // Create urgent service order
        const serviceOrder = new ServiceOrder({
          title: `Pilne zgłoszenie serwisowe - ${transcription.filename}`,
          description: `Automatycznie utworzone z transkrypcji: ${transcription.text}`,
          priority: analysis.urgencyLevel >= 9 ? 'urgent' : 'high',
          type: 'emergency',
          category: 'service',
          stage: 'BACKLOG',
          estimatedCost: this.estimateCostFromTranscription(transcription.text),
          // Note: client would need to be identified from email
          createdBy: null // System created
        });
        
        await serviceOrder.save();
        console.log(`🚨 Created urgent service order from transcription: ${serviceOrder.orderNumber}`);
      }
      
      if (analysis.intent === 'quote_request') {
        // Create sales opportunity
        const opportunity = new Opportunity({
          name: `Oferta z transkrypcji - ${transcription.filename}`,
          description: `Automatycznie utworzone z transkrypcji: ${transcription.text}`,
          stage: 'QUALIFICATION',
          value: this.estimateValueFromTranscription(transcription.text),
          serviceType: this.determineServiceType(transcription.text),
          priority: analysis.urgencyLevel >= 7 ? 'high' : 'medium',
          source: 'phone_transcription',
          // Note: client would need to be identified from email
          createdBy: null // System created
        });
        
        await opportunity.save();
        console.log(`💰 Created sales opportunity from transcription: ${opportunity.name}`);
      }
      
    } catch (error) {
      console.error('❌ Error creating tasks from transcription:', error);
    }
  }

  // Helper methods
  async saveAttachmentToTemp(attachment) {
    const filename = `${Date.now()}_${attachment.filename}`;
    const filePath = path.join(this.tempDir, filename);
    await fs.writeFile(filePath, attachment.payload);
    return filePath;
  }
  
  async cleanupTempFile(filePath) {
    try {
      await fs.unlink(filePath);
    } catch (error) {
      console.error('Error cleaning up temp file:', error);
    }
  }
  
  extractSentiment(analysis) {
    if (!analysis) return 'neutral';
    if (analysis.includes('positive') || analysis.includes('pozytywny')) return 'positive';
    if (analysis.includes('negative') || analysis.includes('negatywny')) return 'negative';
    return 'neutral';
  }
  
  extractIntent(analysis) {
    if (!analysis) return 'unknown';
    if (analysis.includes('serwis') || analysis.includes('naprawa')) return 'service_request';
    if (analysis.includes('oferta') || analysis.includes('wycena')) return 'quote_request';
    return 'general_inquiry';
  }
  
  extractKeywords(text) {
    const hvacKeywords = ['klimatyzacja', 'wentylacja', 'pompa ciepła', 'serwis', 'naprawa', 'instalacja', 'Daikin', 'LG', 'Mitsubishi', 'Samsung'];
    return hvacKeywords.filter(keyword => text.toLowerCase().includes(keyword.toLowerCase()));
  }
  
  isServiceRequest(text) {
    const serviceIndicators = ['problem', 'naprawa', 'serwis', 'nie działa', 'zepsuty', 'awaria'];
    return serviceIndicators.some(indicator => text.toLowerCase().includes(indicator));
  }
  
  extractUrgency(analysis) {
    if (!analysis) return 5;
    const urgencyMatch = analysis.match(/urgency.*?(\d+)/i);
    return urgencyMatch ? parseInt(urgencyMatch[1]) : 5;
  }
  
  estimateCostFromTranscription(text) {
    // Simple cost estimation based on keywords
    if (text.includes('instalacja')) return 5000;
    if (text.includes('naprawa')) return 800;
    if (text.includes('serwis')) return 300;
    return 500;
  }
  
  estimateValueFromTranscription(text) {
    if (text.includes('biurowiec') || text.includes('firma')) return 50000;
    if (text.includes('dom') || text.includes('mieszkanie')) return 15000;
    if (text.includes('restauracja') || text.includes('sklep')) return 25000;
    return 10000;
  }
  
  determineServiceType(text) {
    if (text.includes('instalacja')) return 'installation';
    if (text.includes('naprawa')) return 'repair';
    if (text.includes('serwis')) return 'maintenance';
    return 'consultation';
  }
}

module.exports = new TranscriptionService();
