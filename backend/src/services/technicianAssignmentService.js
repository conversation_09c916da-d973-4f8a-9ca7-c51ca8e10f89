const Technician = require('@/models/appModels/Technician');
const ServiceOrder = require('@/models/appModels/ServiceOrder');
const aiService = require('./aiService');

/**
 * AI-Powered Technician Assignment Service
 * Intelligently assigns technicians to service orders based on multiple factors
 */

class TechnicianAssignmentService {
  constructor() {
    // Default weights for assignment factors
    this.defaultWeights = {
      skillMatch: 0.30,      // How well technician skills match job requirements
      location: 0.25,        // Distance from technician to job location
      workload: 0.20,        // Current workload of technician
      performance: 0.15,     // Historical performance metrics
      availability: 0.10     // Current availability status
    };
  }

  /**
   * Find the best technician for a service order using AI
   */
  async assignBestTechnician(serviceOrder, options = {}) {
    try {
      console.log(`🤖 Starting AI technician assignment for order: ${serviceOrder._id}`);
      
      // Get all available technicians
      const availableTechnicians = await this.getAvailableTechnicians(serviceOrder);
      
      if (availableTechnicians.length === 0) {
        console.log('❌ No available technicians found');
        return null;
      }
      
      // Calculate scores for each technician
      const technicianScores = await this.calculateTechnicianScores(
        serviceOrder, 
        availableTechnicians, 
        options
      );
      
      // Sort by total score (highest first)
      technicianScores.sort((a, b) => b.totalScore - a.totalScore);
      
      // Get the best match
      const bestMatch = technicianScores[0];
      
      console.log(`✅ Best technician assigned: ${bestMatch.technician.name} (Score: ${bestMatch.totalScore.toFixed(2)})`);
      
      // Update technician workload
      await this.updateTechnicianWorkload(bestMatch.technician._id);
      
      return {
        technician: bestMatch.technician,
        score: bestMatch.totalScore,
        reasoning: bestMatch.reasoning,
        alternatives: technicianScores.slice(1, 3) // Top 2 alternatives
      };
      
    } catch (error) {
      console.error('❌ Error in AI technician assignment:', error);
      throw error;
    }
  }

  /**
   * Get available technicians based on service order requirements
   */
  async getAvailableTechnicians(serviceOrder) {
    const query = {
      enabled: true,
      removed: false,
      status: { $in: ['available', 'busy'] },
      currentWorkload: { $lt: 100 }
    };
    
    // Filter by specialization if specified
    if (serviceOrder.requiredSkills && serviceOrder.requiredSkills.length > 0) {
      query.specializations = { $in: serviceOrder.requiredSkills };
    }
    
    // Filter by service area if location is specified
    if (serviceOrder.location && serviceOrder.location.district) {
      query['serviceAreas.district'] = serviceOrder.location.district;
    }
    
    return await Technician.find(query);
  }

  /**
   * Calculate comprehensive scores for each technician
   */
  async calculateTechnicianScores(serviceOrder, technicians, options = {}) {
    const weights = { ...this.defaultWeights, ...options.weights };
    const scores = [];
    
    for (const technician of technicians) {
      const score = await this.calculateIndividualScore(serviceOrder, technician, weights);
      scores.push(score);
    }
    
    return scores;
  }

  /**
   * Calculate individual technician score
   */
  async calculateIndividualScore(serviceOrder, technician, weights) {
    // 1. Skill Match Score (0-1)
    const skillScore = this.calculateSkillMatchScore(serviceOrder, technician);
    
    // 2. Location Score (0-1)
    const locationScore = this.calculateLocationScore(serviceOrder, technician);
    
    // 3. Workload Score (0-1) - lower workload = higher score
    const workloadScore = this.calculateWorkloadScore(technician);
    
    // 4. Performance Score (0-1)
    const performanceScore = this.calculatePerformanceScore(technician);
    
    // 5. Availability Score (0-1)
    const availabilityScore = this.calculateAvailabilityScore(serviceOrder, technician);
    
    // Calculate weighted total score
    const totalScore = (
      skillScore * weights.skillMatch +
      locationScore * weights.location +
      workloadScore * weights.workload +
      performanceScore * weights.performance +
      availabilityScore * weights.availability
    );
    
    // Generate AI reasoning
    const reasoning = await this.generateAssignmentReasoning(
      serviceOrder, 
      technician, 
      {
        skillScore,
        locationScore,
        workloadScore,
        performanceScore,
        availabilityScore,
        totalScore
      }
    );
    
    return {
      technician,
      totalScore,
      breakdown: {
        skillScore,
        locationScore,
        workloadScore,
        performanceScore,
        availabilityScore
      },
      reasoning
    };
  }

  /**
   * Calculate skill match score
   */
  calculateSkillMatchScore(serviceOrder, technician) {
    if (!serviceOrder.requiredSkills || serviceOrder.requiredSkills.length === 0) {
      return 1.0; // No specific skills required
    }
    
    const matchingSkills = serviceOrder.requiredSkills.filter(skill =>
      technician.specializations.includes(skill)
    );
    
    const baseScore = matchingSkills.length / serviceOrder.requiredSkills.length;
    
    // Bonus for experience level
    const experienceBonus = {
      'junior': 0,
      'intermediate': 0.1,
      'senior': 0.2,
      'expert': 0.3
    };
    
    return Math.min(1.0, baseScore + (experienceBonus[technician.experienceLevel] || 0));
  }

  /**
   * Calculate location score based on distance and travel time
   */
  calculateLocationScore(serviceOrder, technician) {
    if (!serviceOrder.location || !technician.currentLocation.coordinates) {
      return 0.5; // Neutral score if location data is missing
    }
    
    // Check if technician serves this area
    if (serviceOrder.location.district) {
      const serviceArea = technician.serviceAreas.find(
        area => area.district === serviceOrder.location.district
      );
      
      if (serviceArea) {
        // Use preference level and travel time
        const preferenceScore = serviceArea.preferenceLevel / 5;
        const travelScore = Math.max(0, 1 - (serviceArea.travelTime / 120)); // 2 hours max
        return (preferenceScore + travelScore) / 2;
      }
    }
    
    // Fallback to distance calculation
    return technician.calculateDistanceScore(serviceOrder.location);
  }

  /**
   * Calculate workload score (lower workload = higher score)
   */
  calculateWorkloadScore(technician) {
    return Math.max(0, 1 - (technician.currentWorkload / 100));
  }

  /**
   * Calculate performance score based on historical metrics
   */
  calculatePerformanceScore(technician) {
    const metrics = technician.performanceMetrics;
    
    const ratingScore = (metrics.averageRating - 1) / 4; // Normalize 1-5 to 0-1
    const onTimeScore = metrics.onTimeCompletionRate / 100;
    const satisfactionScore = (metrics.customerSatisfactionScore - 1) / 4;
    const fixRateScore = metrics.firstTimeFixRate / 100;
    
    return (ratingScore + onTimeScore + satisfactionScore + fixRateScore) / 4;
  }

  /**
   * Calculate availability score based on schedule and current status
   */
  calculateAvailabilityScore(serviceOrder, technician) {
    // Base score from status
    const statusScores = {
      'available': 1.0,
      'busy': 0.7,
      'on_break': 0.3,
      'off_duty': 0.0,
      'vacation': 0.0,
      'sick': 0.0
    };
    
    let score = statusScores[technician.status] || 0;
    
    // Check if scheduled date falls within working hours
    if (serviceOrder.scheduledDate) {
      const dayOfWeek = serviceOrder.scheduledDate.toLocaleLowerCase();
      const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
      const dayName = dayNames[serviceOrder.scheduledDate.getDay()];
      
      const workingDay = technician.workingHours[dayName];
      if (workingDay && workingDay.available) {
        score *= 1.2; // Bonus for being available on the scheduled day
      } else {
        score *= 0.5; // Penalty for not being available on scheduled day
      }
    }
    
    return Math.min(1.0, score);
  }

  /**
   * Generate AI-powered reasoning for the assignment
   */
  async generateAssignmentReasoning(serviceOrder, technician, scores) {
    try {
      const prompt = `
        Generate a brief explanation for why this technician is a good match for this service order:
        
        Technician: ${technician.name}
        - Experience: ${technician.experienceLevel} (${technician.yearsOfExperience} years)
        - Specializations: ${technician.specializations.join(', ')}
        - Current workload: ${technician.currentWorkload}%
        - Performance rating: ${technician.performanceMetrics.averageRating}/5
        
        Service Order:
        - Type: ${serviceOrder.type}
        - Priority: ${serviceOrder.priority}
        - Required skills: ${serviceOrder.requiredSkills?.join(', ') || 'None specified'}
        - Location: ${serviceOrder.location?.district || 'Not specified'}
        
        Scores:
        - Skill match: ${(scores.skillScore * 100).toFixed(0)}%
        - Location: ${(scores.locationScore * 100).toFixed(0)}%
        - Workload: ${(scores.workloadScore * 100).toFixed(0)}%
        - Performance: ${(scores.performanceScore * 100).toFixed(0)}%
        - Availability: ${(scores.availabilityScore * 100).toFixed(0)}%
        - Total: ${(scores.totalScore * 100).toFixed(0)}%
        
        Provide a concise 2-3 sentence explanation focusing on the strongest factors.
      `;
      
      const reasoning = await aiService.generateResponse(prompt);
      return reasoning.trim();
      
    } catch (error) {
      console.error('Error generating AI reasoning:', error);
      return `Selected based on skill match (${(scores.skillScore * 100).toFixed(0)}%) and availability (${(scores.availabilityScore * 100).toFixed(0)}%).`;
    }
  }

  /**
   * Update technician workload after assignment
   */
  async updateTechnicianWorkload(technicianId) {
    try {
      // Count active service orders for this technician
      const activeOrders = await ServiceOrder.countDocuments({
        assignedTechnician: technicianId,
        stage: { $nin: ['COMPLETED', 'BILLED', 'CLOSED'] },
        removed: false
      });
      
      // Calculate workload percentage (assuming max 6 jobs per day)
      const maxDailyJobs = 6;
      const workloadPercentage = Math.min(100, (activeOrders / maxDailyJobs) * 100);
      
      await Technician.findByIdAndUpdate(technicianId, {
        currentWorkload: workloadPercentage
      });
      
      console.log(`📊 Updated technician workload: ${workloadPercentage}%`);
      
    } catch (error) {
      console.error('Error updating technician workload:', error);
    }
  }

  /**
   * Get assignment recommendations for a service order
   */
  async getAssignmentRecommendations(serviceOrder, limit = 5) {
    try {
      const availableTechnicians = await this.getAvailableTechnicians(serviceOrder);
      const technicianScores = await this.calculateTechnicianScores(serviceOrder, availableTechnicians);
      
      // Sort by score and return top recommendations
      return technicianScores
        .sort((a, b) => b.totalScore - a.totalScore)
        .slice(0, limit)
        .map(score => ({
          technician: {
            id: score.technician._id,
            name: score.technician.name,
            specializations: score.technician.specializations,
            currentWorkload: score.technician.currentWorkload,
            rating: score.technician.performanceMetrics.averageRating
          },
          score: score.totalScore,
          reasoning: score.reasoning,
          breakdown: score.breakdown
        }));
        
    } catch (error) {
      console.error('Error getting assignment recommendations:', error);
      throw error;
    }
  }
}

module.exports = new TechnicianAssignmentService();
