const nodemailer = require('nodemailer');
const Imap = require('imap');
const { simpleParser } = require('mailparser');
const fs = require('fs').promises;
const path = require('path');
const Email = require('../models/appModels/Email');
const invoiceAnalysisService = require('./invoiceAnalysisService');
const enhancedAIService = require('./enhancedAIService');

class EmailProcessingService {
  constructor() {
    this.imapConfig = {
      user: process.env.EMAIL_USER || '<EMAIL>',
      password: process.env.EMAIL_PASS || 'Blaeritipol1',
      host: process.env.EMAIL_HOST || 'imap.gmail.com',
      port: 993,
      tls: true,
      tlsOptions: { rejectUnauthorized: false }
    };
    
    this.grzegorzImapConfig = {
      user: '<EMAIL>',
      password: 'Blaeritipol1',
      host: 'imap.gmail.com',
      port: 993,
      tls: true,
      tlsOptions: { rejectUnauthorized: false }
    };
  }

  /**
   * MAIN EMAIL PROCESSING PIPELINE
   * Processes emails from both dolores@ and grzegorz@ accounts
   */
  async processAllEmails() {
    try {
      console.log('🚀 Starting comprehensive email processing pipeline...');
      
      // Process both email accounts
      const doloresResults = await this.processEmailAccount(this.imapConfig, 'dolores');
      const grzegorzResults = await this.processEmailAccount(this.grzegorzImapConfig, 'grzegorz');
      
      const totalProcessed = doloresResults.processed + grzegorzResults.processed;
      const totalM4A = doloresResults.m4aFiles + grzegorzResults.m4aFiles;
      const totalInvoices = doloresResults.invoices + grzegorzResults.invoices;
      
      console.log(`✅ Email processing completed!`);
      console.log(`📧 Total emails processed: ${totalProcessed}`);
      console.log(`🎵 M4A files found: ${totalM4A}`);
      console.log(`📄 Invoices analyzed: ${totalInvoices}`);
      
      return {
        success: true,
        totalProcessed,
        totalM4A,
        totalInvoices,
        doloresResults,
        grzegorzResults,
        timestamp: new Date()
      };
      
    } catch (error) {
      console.error('❌ Email processing pipeline error:', error);
      throw error;
    }
  }

  /**
   * PROCESS SINGLE EMAIL ACCOUNT
   * Connects to IMAP and processes all unread emails
   */
  async processEmailAccount(config, accountName) {
    return new Promise((resolve, reject) => {
      const imap = new Imap(config);
      let processed = 0;
      let m4aFiles = 0;
      let invoices = 0;
      
      imap.once('ready', () => {
        console.log(`📬 Connected to ${accountName}@koldbringers.pl`);
        
        imap.openBox('INBOX', false, (err, box) => {
          if (err) {
            reject(err);
            return;
          }
          
          // Search for unread emails from last 30 days
          const searchCriteria = ['UNSEEN', ['SINCE', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)]];
          
          imap.search(searchCriteria, (err, results) => {
            if (err) {
              reject(err);
              return;
            }
            
            if (!results || results.length === 0) {
              console.log(`📭 No new emails found in ${accountName} account`);
              imap.end();
              resolve({ processed: 0, m4aFiles: 0, invoices: 0 });
              return;
            }
            
            console.log(`📨 Found ${results.length} unread emails in ${accountName} account`);
            
            const fetch = imap.fetch(results, {
              bodies: '',
              struct: true,
              markSeen: false
            });
            
            fetch.on('message', (msg, seqno) => {
              let emailData = {};
              
              msg.on('body', (stream, info) => {
                let buffer = '';
                stream.on('data', (chunk) => {
                  buffer += chunk.toString('utf8');
                });
                
                stream.once('end', async () => {
                  try {
                    const parsed = await simpleParser(buffer);
                    emailData = {
                      subject: parsed.subject,
                      from: parsed.from?.text,
                      to: parsed.to?.text,
                      date: parsed.date,
                      text: parsed.text,
                      html: parsed.html,
                      attachments: parsed.attachments || [],
                      messageId: parsed.messageId,
                      accountName
                    };
                    
                    // Process email and attachments
                    const result = await this.processEmailData(emailData);
                    processed++;
                    m4aFiles += result.m4aCount || 0;
                    invoices += result.invoiceCount || 0;
                    
                  } catch (error) {
                    console.error(`❌ Error processing email ${seqno}:`, error);
                  }
                });
              });
            });
            
            fetch.once('end', () => {
              console.log(`✅ Finished processing ${accountName} account`);
              imap.end();
              resolve({ processed, m4aFiles, invoices });
            });
            
            fetch.once('error', (err) => {
              console.error(`❌ Fetch error for ${accountName}:`, err);
              imap.end();
              reject(err);
            });
          });
        });
      });
      
      imap.once('error', (err) => {
        console.error(`❌ IMAP error for ${accountName}:`, err);
        reject(err);
      });
      
      imap.connect();
    });
  }

  /**
   * PROCESS INDIVIDUAL EMAIL DATA
   * Saves email to database and processes attachments
   */
  async processEmailData(emailData) {
    try {
      // Check if email already exists
      const existingEmail = await Email.findOne({ messageId: emailData.messageId });
      if (existingEmail) {
        console.log(`📧 Email already processed: ${emailData.subject}`);
        return { m4aCount: 0, invoiceCount: 0 };
      }
      
      // Create email record
      const email = new Email({
        subject: emailData.subject,
        content: emailData.text,
        htmlContent: emailData.html,
        sender: emailData.from,
        senderEmail: this.extractEmail(emailData.from),
        recipients: this.parseRecipients(emailData.to),
        messageId: emailData.messageId,
        receivedAt: emailData.date,
        source: 'imap',
        sourceAccountId: emailData.accountName,
        attachments: []
      });
      
      let m4aCount = 0;
      let invoiceCount = 0;
      
      // Process attachments
      for (const attachment of emailData.attachments) {
        const attachmentData = {
          filename: attachment.filename,
          contentType: attachment.contentType,
          size: attachment.size,
          payload: attachment.content
        };
        
        // Check for M4A files (transcriptions)
        if (attachment.filename?.toLowerCase().endsWith('.m4a')) {
          attachmentData.isM4A = true;
          m4aCount++;
          
          // TODO: Queue for transcription processing
          console.log(`🎵 M4A file found: ${attachment.filename}`);
        }
        
        // Check for invoice files (PDF, images)
        if (this.isInvoiceFile(attachment.filename, attachment.contentType)) {
          invoiceCount++;
          
          // Process invoice with AI
          try {
            const invoiceResult = await invoiceAnalysisService.analyzeAttachment(attachment);
            email.invoiceAnalysis = {
              analyzed: true,
              analyzedAt: new Date(),
              confidence: invoiceResult.confidence,
              category: invoiceResult.category,
              amount: invoiceResult.amount,
              extractedData: invoiceResult.extractedData
            };
            
            console.log(`📄 Invoice analyzed: ${attachment.filename} - ${invoiceResult.category}`);
          } catch (error) {
            console.error(`❌ Invoice analysis error for ${attachment.filename}:`, error);
          }
        }
        
        email.attachments.push(attachmentData);
      }
      
      // AI analysis of email content
      try {
        const aiAnalysis = await this.performEmailAIAnalysis(emailData);
        email.aiAnalysis = aiAnalysis;
        email.hvacData = this.extractHVACData(emailData, aiAnalysis);
      } catch (error) {
        console.error('❌ AI analysis error:', error);
      }
      
      // Save email to database
      await email.save();
      console.log(`✅ Email saved: ${emailData.subject}`);
      
      return { m4aCount, invoiceCount };
      
    } catch (error) {
      console.error('❌ Error processing email data:', error);
      throw error;
    }
  }

  /**
   * AI ANALYSIS OF EMAIL CONTENT
   * Uses enhanced AI service for intelligent email analysis
   */
  async performEmailAIAnalysis(emailData) {
    try {
      // Use LM Studio for local AI analysis
      const response = await fetch(`${process.env.LM_STUDIO_URL}/v1/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: process.env.LM_STUDIO_MODEL,
          messages: [
            {
              role: 'system',
              content: 'Jesteś ekspertem analizy emaili HVAC. Analizuj treść emaili pod kątem: sentymentu, intencji, pilności, typu usługi HVAC.'
            },
            {
              role: 'user',
              content: `Przeanalizuj ten email HVAC:
              
              Temat: ${emailData.subject}
              Nadawca: ${emailData.from}
              Treść: ${emailData.text}
              
              Określ: sentiment (positive/negative/neutral), intent, urgencyLevel (1-10), isServiceRequest, isQuoteRequest`
            }
          ],
          temperature: 0.3,
          max_tokens: 500
        })
      });
      
      const result = await response.json();
      const analysis = result.choices[0]?.message?.content;
      
      // Parse AI response (simplified)
      return {
        sentiment: this.extractSentiment(analysis),
        intent: this.extractIntent(analysis),
        urgencyLevel: this.extractUrgency(analysis),
        isServiceRequest: analysis?.includes('serwis') || analysis?.includes('naprawa'),
        isQuoteRequest: analysis?.includes('oferta') || analysis?.includes('wycena'),
        rawAnalysis: analysis
      };
      
    } catch (error) {
      console.error('❌ AI analysis error:', error);
      return {
        sentiment: 'neutral',
        intent: 'unknown',
        urgencyLevel: 5,
        isServiceRequest: false,
        isQuoteRequest: false
      };
    }
  }

  // Helper methods
  extractEmail(fromString) {
    const match = fromString?.match(/<(.+)>/);
    return match ? match[1] : fromString;
  }
  
  parseRecipients(toString) {
    if (!toString) return [];
    return toString.split(',').map(email => ({
      email: this.extractEmail(email.trim()),
      type: 'to'
    }));
  }
  
  isInvoiceFile(filename, contentType) {
    if (!filename) return false;
    const lowerFilename = filename.toLowerCase();
    return lowerFilename.includes('faktura') || 
           lowerFilename.includes('invoice') || 
           lowerFilename.includes('rachunek') ||
           contentType?.includes('pdf') ||
           contentType?.includes('image');
  }
  
  extractSentiment(analysis) {
    if (!analysis) return 'neutral';
    if (analysis.includes('positive') || analysis.includes('pozytywny')) return 'positive';
    if (analysis.includes('negative') || analysis.includes('negatywny')) return 'negative';
    return 'neutral';
  }
  
  extractIntent(analysis) {
    if (!analysis) return 'unknown';
    if (analysis.includes('serwis')) return 'service_request';
    if (analysis.includes('oferta')) return 'quote_request';
    if (analysis.includes('reklamacja')) return 'complaint';
    return 'general_inquiry';
  }
  
  extractUrgency(analysis) {
    if (!analysis) return 5;
    const urgencyMatch = analysis.match(/urgency.*?(\d+)/i);
    return urgencyMatch ? parseInt(urgencyMatch[1]) : 5;
  }
  
  extractHVACData(emailData, aiAnalysis) {
    return {
      isServiceRelated: aiAnalysis.isServiceRequest,
      serviceType: aiAnalysis.isServiceRequest ? 'maintenance' : 
                   aiAnalysis.isQuoteRequest ? 'quote' : 'inquiry',
      estimatedValue: this.estimateValueFromContent(emailData.text),
      customerType: this.determineCustomerType(emailData.text)
    };
  }
  
  estimateValueFromContent(text) {
    if (!text) return 0;
    const priceMatch = text.match(/(\d+)\s*(zł|pln|złoty)/i);
    return priceMatch ? parseInt(priceMatch[1]) : 0;
  }
  
  determineCustomerType(text) {
    if (!text) return 'residential';
    if (text.includes('firma') || text.includes('biuro') || text.includes('commercial')) return 'commercial';
    if (text.includes('fabryka') || text.includes('industrial')) return 'industrial';
    return 'residential';
  }
}

module.exports = new EmailProcessingService();
