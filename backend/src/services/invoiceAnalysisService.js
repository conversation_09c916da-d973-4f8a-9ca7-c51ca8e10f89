/**
 * INVOICE ANALYSIS SERVICE
 * AI-powered invoice analysis from email attachments
 * Integrates with Email Intelligence System for HVAC CRM
 */

const fs = require('fs').promises;
const path = require('path');
const pdf = require('pdf-parse');
const Tesseract = require('tesseract.js');
const OpenAI = require('openai');

// Import models
const Invoice = require('@/models/appModels/Invoice');
const Email = require('@/models/appModels/Email');
const Client = require('@/models/appModels/Client');

// Import services
const aiService = require('@/services/aiService');
const enhancedAIService = require('@/services/enhancedAIService');

// Initialize OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

/**
 * MAIN INVOICE ANALYSIS FUNCTIONS
 */

// Analyze invoice from email attachment
const analyzeInvoiceFromEmail = async (emailId, attachmentId) => {
  try {
    console.log(`🧾 Starting invoice analysis for email ${emailId}, attachment ${attachmentId}`);

    // Get email and attachment
    const email = await Email.findById(emailId);
    if (!email) {
      throw new Error('Email not found');
    }

    const attachment = email.attachments.find(att => att._id.toString() === attachmentId);
    if (!attachment) {
      throw new Error('Attachment not found');
    }

    // Check if attachment is invoice-like
    if (!isInvoiceAttachment(attachment)) {
      throw new Error('Attachment is not an invoice document');
    }

    // Extract text from attachment
    const extractedText = await extractTextFromAttachment(attachment);
    
    // Analyze with AI
    const analysisResult = await analyzeInvoiceWithAI(extractedText, email);
    
    // Create or update invoice record
    const invoiceRecord = await createInvoiceRecord(analysisResult, email, attachment);
    
    // Update email with analysis results
    await updateEmailWithInvoiceAnalysis(emailId, analysisResult, invoiceRecord._id);

    console.log(`✅ Invoice analysis completed for ${attachment.filename}`);
    
    return {
      success: true,
      invoiceId: invoiceRecord._id,
      analysis: analysisResult,
      extractedText: extractedText.substring(0, 500) + '...' // First 500 chars for preview
    };

  } catch (error) {
    console.error(`❌ Invoice analysis failed: ${error.message}`);
    throw error;
  }
};

// Batch analyze all invoice attachments from recent emails
const batchAnalyzeInvoices = async (daysBack = 30) => {
  try {
    console.log(`🔄 Starting batch invoice analysis for last ${daysBack} days`);

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysBack);

    // Find emails with potential invoice attachments
    const emails = await Email.find({
      receivedAt: { $gte: cutoffDate },
      'attachments.0': { $exists: true },
      'invoiceAnalysis.analyzed': { $ne: true }
    });

    const results = [];
    let processedCount = 0;
    let errorCount = 0;

    for (const email of emails) {
      for (const attachment of email.attachments) {
        if (isInvoiceAttachment(attachment)) {
          try {
            const result = await analyzeInvoiceFromEmail(email._id, attachment._id);
            results.push({
              emailId: email._id,
              attachmentId: attachment._id,
              filename: attachment.filename,
              success: true,
              invoiceId: result.invoiceId
            });
            processedCount++;
          } catch (error) {
            results.push({
              emailId: email._id,
              attachmentId: attachment._id,
              filename: attachment.filename,
              success: false,
              error: error.message
            });
            errorCount++;
          }
        }
      }
    }

    console.log(`✅ Batch analysis completed: ${processedCount} processed, ${errorCount} errors`);
    
    return {
      success: true,
      totalProcessed: processedCount,
      totalErrors: errorCount,
      results
    };

  } catch (error) {
    console.error(`❌ Batch invoice analysis failed: ${error.message}`);
    throw error;
  }
};

/**
 * TEXT EXTRACTION FUNCTIONS
 */

// Extract text from various attachment types
const extractTextFromAttachment = async (attachment) => {
  try {
    const fileExtension = path.extname(attachment.filename).toLowerCase();
    const fileBuffer = attachment.payload;

    switch (fileExtension) {
      case '.pdf':
        return await extractTextFromPDF(fileBuffer);
      case '.jpg':
      case '.jpeg':
      case '.png':
      case '.tiff':
        return await extractTextFromImage(fileBuffer);
      case '.txt':
        return fileBuffer.toString('utf-8');
      default:
        throw new Error(`Unsupported file type: ${fileExtension}`);
    }
  } catch (error) {
    console.error(`❌ Text extraction failed: ${error.message}`);
    throw error;
  }
};

// Extract text from PDF using pdf-parse
const extractTextFromPDF = async (pdfBuffer) => {
  try {
    const data = await pdf(pdfBuffer);
    return data.text;
  } catch (error) {
    console.error(`❌ PDF text extraction failed: ${error.message}`);
    throw error;
  }
};

// Extract text from image using Tesseract OCR
const extractTextFromImage = async (imageBuffer) => {
  try {
    const { data: { text } } = await Tesseract.recognize(imageBuffer, 'pol', {
      logger: m => console.log(`OCR Progress: ${m.status} ${m.progress}`)
    });
    return text;
  } catch (error) {
    console.error(`❌ OCR text extraction failed: ${error.message}`);
    throw error;
  }
};

/**
 * AI ANALYSIS FUNCTIONS
 */

// Analyze invoice text with AI
const analyzeInvoiceWithAI = async (extractedText, email) => {
  try {
    const prompt = `
    Analizujesz fakturę HVAC dla polskiej firmy. Wyciągnij następujące informacje z tekstu faktury:

    TEKST FAKTURY:
    ${extractedText}

    KONTEKST EMAIL:
    Od: ${email.sender} (${email.senderEmail})
    Temat: ${email.subject}
    Data: ${email.receivedAt}

    Zwróć JSON z następującymi polami:
    {
      "invoice_number": "numer faktury",
      "issue_date": "data wystawienia (YYYY-MM-DD)",
      "due_date": "termin płatności (YYYY-MM-DD)",
      "vendor": {
        "name": "nazwa dostawcy",
        "address": "adres dostawcy",
        "tax_id": "NIP dostawcy",
        "email": "email dostawcy"
      },
      "customer": {
        "name": "nazwa klienta",
        "address": "adres klienta",
        "tax_id": "NIP klienta"
      },
      "amounts": {
        "net_amount": 0.00,
        "tax_amount": 0.00,
        "gross_amount": 0.00,
        "currency": "PLN"
      },
      "line_items": [
        {
          "description": "opis pozycji",
          "quantity": 1,
          "unit_price": 0.00,
          "net_amount": 0.00,
          "tax_rate": 23
        }
      ],
      "hvac_category": "kategoria HVAC (klimatyzacja/wentylacja/pompa_ciepla/serwis)",
      "equipment_brands": ["wykryte marki sprzętu"],
      "service_type": "typ usługi",
      "confidence_score": 0.95,
      "analysis_notes": "dodatkowe uwagi"
    }

    Zwróć tylko poprawny JSON bez dodatkowych komentarzy.
    `;

    const completion = await openai.chat.completions.create({
      model: "gpt-4-turbo-preview",
      messages: [
        {
          role: "system",
          content: "Jesteś ekspertem w analizie faktur HVAC dla polskiego rynku. Zwracasz tylko poprawny JSON."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.1,
      max_tokens: 2000
    });

    const analysisText = completion.choices[0].message.content;
    
    // Parse JSON response
    let analysisResult;
    try {
      analysisResult = JSON.parse(analysisText);
    } catch (parseError) {
      // Fallback: extract JSON from response
      const jsonMatch = analysisText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        analysisResult = JSON.parse(jsonMatch[0]);
      } else {
        throw new Error('Failed to parse AI response as JSON');
      }
    }

    // Validate and enhance analysis
    analysisResult = await enhanceAnalysisResult(analysisResult, extractedText);

    return analysisResult;

  } catch (error) {
    console.error(`❌ AI invoice analysis failed: ${error.message}`);
    throw error;
  }
};

/**
 * HELPER FUNCTIONS
 */

// Check if attachment is likely an invoice
const isInvoiceAttachment = (attachment) => {
  const filename = attachment.filename.toLowerCase();
  const invoiceKeywords = [
    'faktura', 'invoice', 'rachunek', 'bill', 'nota', 'paragon',
    'fv', 'inv', 'receipt', 'payment'
  ];
  
  const supportedExtensions = ['.pdf', '.jpg', '.jpeg', '.png', '.tiff', '.txt'];
  const hasValidExtension = supportedExtensions.some(ext => filename.endsWith(ext));
  const hasInvoiceKeyword = invoiceKeywords.some(keyword => filename.includes(keyword));
  
  return hasValidExtension && (hasInvoiceKeyword || attachment.contentType?.includes('pdf'));
};

// Enhance analysis result with additional processing
const enhanceAnalysisResult = async (analysisResult, extractedText) => {
  try {
    // Add metadata
    analysisResult.metadata = {
      analyzed_at: new Date(),
      text_length: extractedText.length,
      ai_model: 'gpt-4-turbo-preview',
      extraction_method: 'hybrid_ai_ocr'
    };

    // Validate amounts
    if (analysisResult.amounts) {
      const { net_amount, tax_amount, gross_amount } = analysisResult.amounts;
      if (net_amount && tax_amount && !gross_amount) {
        analysisResult.amounts.gross_amount = net_amount + tax_amount;
      }
    }

    // Enhance HVAC categorization
    if (!analysisResult.hvac_category) {
      analysisResult.hvac_category = detectHVACCategory(extractedText);
    }

    // Detect equipment brands
    if (!analysisResult.equipment_brands || analysisResult.equipment_brands.length === 0) {
      analysisResult.equipment_brands = detectEquipmentBrands(extractedText);
    }

    return analysisResult;

  } catch (error) {
    console.error(`❌ Analysis enhancement failed: ${error.message}`);
    return analysisResult; // Return original if enhancement fails
  }
};

// Detect HVAC category from text
const detectHVACCategory = (text) => {
  const categories = {
    'klimatyzacja': ['klimatyzacja', 'klimatyzator', 'split', 'multi split', 'vrf'],
    'wentylacja': ['wentylacja', 'wentylator', 'rekuperator', 'nawiew', 'wywiew'],
    'pompa_ciepla': ['pompa ciepła', 'heat pump', 'pompa'],
    'serwis': ['serwis', 'naprawa', 'konserwacja', 'przegląd', 'maintenance']
  };

  const textLower = text.toLowerCase();
  
  for (const [category, keywords] of Object.entries(categories)) {
    if (keywords.some(keyword => textLower.includes(keyword))) {
      return category;
    }
  }
  
  return 'inne';
};

// Detect equipment brands from text
const detectEquipmentBrands = (text) => {
  const brands = ['daikin', 'lg', 'mitsubishi', 'carrier', 'toshiba', 'panasonic', 'fujitsu', 'gree', 'haier', 'samsung'];
  const textLower = text.toLowerCase();
  
  return brands.filter(brand => textLower.includes(brand));
};

/**
 * DATABASE OPERATIONS
 */

// Create invoice record from analysis
const createInvoiceRecord = async (analysisResult, email, attachment) => {
  try {
    // Find or create client based on vendor info
    let client = null;
    if (analysisResult.vendor?.email) {
      client = await Client.findOne({ email: analysisResult.vendor.email });
      if (!client && analysisResult.vendor.name) {
        client = await Client.create({
          name: analysisResult.vendor.name,
          email: analysisResult.vendor.email,
          address: analysisResult.vendor.address,
          taxId: analysisResult.vendor.tax_id,
          type: 'vendor',
          source: 'invoice_analysis'
        });
      }
    }

    // Create invoice record
    const invoiceData = {
      number: analysisResult.invoice_number || `INV-${Date.now()}`,
      date: analysisResult.issue_date ? new Date(analysisResult.issue_date) : new Date(),
      dueDate: analysisResult.due_date ? new Date(analysisResult.due_date) : null,
      client: client?._id,

      // Financial data
      subTotal: analysisResult.amounts?.net_amount || 0,
      taxTotal: analysisResult.amounts?.tax_amount || 0,
      total: analysisResult.amounts?.gross_amount || 0,
      currency: analysisResult.amounts?.currency || 'PLN',

      // Line items
      items: (analysisResult.line_items || []).map(item => ({
        itemName: item.description || 'Pozycja faktury',
        description: item.description,
        quantity: item.quantity || 1,
        price: item.unit_price || 0,
        total: item.net_amount || 0,
        taxRate: item.tax_rate || 23
      })),

      // HVAC specific data
      hvacCategory: analysisResult.hvac_category,
      equipmentBrands: analysisResult.equipment_brands || [],
      serviceType: analysisResult.service_type,

      // Analysis metadata
      analysisData: {
        confidence_score: analysisResult.confidence_score,
        analysis_notes: analysisResult.analysis_notes,
        source_email: email._id,
        source_attachment: attachment._id,
        analyzed_at: new Date(),
        ai_model: analysisResult.metadata?.ai_model
      },

      // Status
      status: 'analyzed',
      paymentStatus: 'pending',

      // Additional fields
      notes: `Automatycznie utworzona z analizy załącznika: ${attachment.filename}`,
      tags: ['auto_generated', 'email_attachment', analysisResult.hvac_category].filter(Boolean)
    };

    const invoice = await Invoice.create(invoiceData);
    console.log(`✅ Created invoice record: ${invoice.number}`);

    return invoice;

  } catch (error) {
    console.error(`❌ Failed to create invoice record: ${error.message}`);
    throw error;
  }
};

// Update email with invoice analysis results
const updateEmailWithInvoiceAnalysis = async (emailId, analysisResult, invoiceId) => {
  try {
    const updateData = {
      'invoiceAnalysis.analyzed': true,
      'invoiceAnalysis.analyzedAt': new Date(),
      'invoiceAnalysis.invoiceId': invoiceId,
      'invoiceAnalysis.confidence': analysisResult.confidence_score,
      'invoiceAnalysis.category': analysisResult.hvac_category,
      'invoiceAnalysis.amount': analysisResult.amounts?.gross_amount,
      'invoiceAnalysis.currency': analysisResult.amounts?.currency || 'PLN'
    };

    await Email.findByIdAndUpdate(emailId, updateData);
    console.log(`✅ Updated email ${emailId} with invoice analysis`);

  } catch (error) {
    console.error(`❌ Failed to update email with analysis: ${error.message}`);
    throw error;
  }
};

// Get invoice analysis statistics
const getInvoiceAnalysisStats = async () => {
  try {
    const stats = {
      totalAnalyzed: await Email.countDocuments({ 'invoiceAnalysis.analyzed': true }),
      totalInvoices: await Invoice.countDocuments({ 'analysisData.source_email': { $exists: true } }),
      byCategory: await Invoice.aggregate([
        { $match: { 'analysisData.source_email': { $exists: true } } },
        { $group: { _id: '$hvacCategory', count: { $sum: 1 }, totalAmount: { $sum: '$total' } } }
      ]),
      byMonth: await Invoice.aggregate([
        { $match: { 'analysisData.source_email': { $exists: true } } },
        { $group: {
          _id: {
            year: { $year: '$date' },
            month: { $month: '$date' }
          },
          count: { $sum: 1 },
          totalAmount: { $sum: '$total' }
        }},
        { $sort: { '_id.year': -1, '_id.month': -1 } },
        { $limit: 12 }
      ]),
      averageConfidence: await Invoice.aggregate([
        { $match: { 'analysisData.confidence_score': { $exists: true } } },
        { $group: { _id: null, avgConfidence: { $avg: '$analysisData.confidence_score' } } }
      ]),
      recentAnalyses: await Invoice.find({ 'analysisData.source_email': { $exists: true } })
        .sort({ 'analysisData.analyzed_at': -1 })
        .limit(10)
        .select('number date total hvacCategory analysisData.confidence_score')
    };

    return stats;

  } catch (error) {
    console.error(`❌ Failed to get invoice analysis stats: ${error.message}`);
    throw error;
  }
};

module.exports = {
  analyzeInvoiceFromEmail,
  batchAnalyzeInvoices,
  extractTextFromAttachment,
  analyzeInvoiceWithAI,
  isInvoiceAttachment,
  createInvoiceRecord,
  updateEmailWithInvoiceAnalysis,
  getInvoiceAnalysisStats
};
