/**
 * CALENDAR SERVICE
 * Enhanced calendar integration with 3-category HVAC system and Warsaw optimization
 * Categories: 🔧 <PERSON>, 🏗️ In<PERSON>, 🔍 Oględziny
 */

const mongoose = require('mongoose');
const moment = require('moment');

// Import models
const ServiceOrder = require('@/models/appModels/ServiceOrder');
const Client = require('@/models/appModels/Client');
const Equipment = require('@/models/appModels/Equipment');

// Import services
const aiService = require('@/services/aiService');

/**
 * WARSAW DISTRICTS CONFIGURATION
 */
const WARSAW_DISTRICTS = {
  'Bemowo': { lat: 52.2394, lng: 20.9186, zone: 'west' },
  'Białołęka': { lat: 52.2962, lng: 21.0122, zone: 'north' },
  'Bielany': { lat: 52.2833, lng: 20.9333, zone: 'north' },
  'Mokotów': { lat: 52.1833, lng: 21.0333, zone: 'south' },
  'Ochota': { lat: 52.2167, lng: 20.9833, zone: 'center' },
  'Praga-Południe': { lat: 52.2333, lng: 21.0833, zone: 'east' },
  'Praga-Północ': { lat: 52.2667, lng: 21.0333, zone: 'east' },
  'Rembertów': { lat: 52.2500, lng: 21.1500, zone: 'east' },
  'Śródmieście': { lat: 52.2333, lng: 21.0167, zone: 'center' },
  'Targówek': { lat: 52.2833, lng: 21.0500, zone: 'north' },
  'Ursus': { lat: 52.1833, lng: 20.8833, zone: 'west' },
  'Ursynów': { lat: 52.1500, lng: 21.0500, zone: 'south' },
  'Wawer': { lat: 52.2167, lng: 21.1167, zone: 'east' },
  'Wesoła': { lat: 52.2333, lng: 21.2167, zone: 'east' },
  'Wilanów': { lat: 52.1667, lng: 21.0833, zone: 'south' },
  'Włochy': { lat: 52.1833, lng: 20.9167, zone: 'west' },
  'Wola': { lat: 52.2333, lng: 20.9667, zone: 'center' },
  'Żoliborz': { lat: 52.2667, lng: 20.9833, zone: 'north' }
};

/**
 * HVAC CATEGORIES CONFIGURATION
 */
const HVAC_CATEGORIES = {
  'serwis': {
    icon: '🔧',
    name: 'Serwis',
    color: '#ff7a45',
    duration: 120, // minutes
    priority: 'high',
    skills: ['hvac_maintenance', 'diagnostics', 'repair']
  },
  'instalacja': {
    icon: '🏗️',
    name: 'Instalacja',
    color: '#52c41a',
    duration: 240, // minutes
    priority: 'medium',
    skills: ['hvac_installation', 'electrical', 'plumbing']
  },
  'oględziny': {
    icon: '🔍',
    name: 'Oględziny',
    color: '#1890ff',
    duration: 60, // minutes
    priority: 'low',
    skills: ['assessment', 'consultation']
  }
};

/**
 * MAIN CALENDAR FUNCTIONS
 */

// Get calendar events with filtering and optimization
const getCalendarEvents = async (startDate, endDate, filters = {}) => {
  try {
    console.log(`📅 Loading calendar events from ${startDate} to ${endDate}`);

    // Build query
    let query = {
      scheduledDate: {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      }
    };

    // Apply filters
    if (filters.category && filters.category !== 'all') {
      query.category = filters.category;
    }

    if (filters.district && filters.district !== 'all') {
      query['location.district'] = filters.district;
    }

    if (filters.technician && filters.technician !== 'all') {
      query.assignedTechnician = filters.technician;
    }

    if (filters.status && filters.status !== 'all') {
      query.status = filters.status;
    }

    // Get service orders
    const serviceOrders = await ServiceOrder.find(query)
      .populate('client', 'name email phone address')
      .populate('equipment', 'name brand model serialNumber')
      .populate('assignedTechnician', 'name email phone skills')
      .sort({ scheduledDate: 1 });

    // Transform to calendar events
    const events = serviceOrders.map(order => transformToCalendarEvent(order));

    // Apply Warsaw optimization if requested
    if (filters.optimizeRoutes) {
      return optimizeWarsawRoutes(events, filters.optimizeDate);
    }

    console.log(`✅ Loaded ${events.length} calendar events`);
    return events;

  } catch (error) {
    console.error(`❌ Failed to load calendar events: ${error.message}`);
    throw error;
  }
};

// Create new calendar event
const createCalendarEvent = async (eventData) => {
  try {
    console.log(`📅 Creating new calendar event: ${eventData.title}`);

    // Validate HVAC category
    if (!HVAC_CATEGORIES[eventData.category]) {
      throw new Error(`Invalid HVAC category: ${eventData.category}`);
    }

    // Get category configuration
    const categoryConfig = HVAC_CATEGORIES[eventData.category];

    // Create service order
    const serviceOrderData = {
      title: eventData.title,
      description: eventData.description,
      category: eventData.category,
      client: eventData.clientId,
      equipment: eventData.equipmentId,
      
      // Schedule data
      scheduledDate: new Date(eventData.scheduledDate),
      estimatedDuration: eventData.duration || categoryConfig.duration,
      
      // Location data
      location: {
        address: eventData.address,
        district: eventData.district,
        coordinates: WARSAW_DISTRICTS[eventData.district] || null
      },
      
      // Assignment
      assignedTechnician: eventData.technicianId,
      
      // Priority and status
      priority: eventData.priority || categoryConfig.priority,
      status: 'scheduled',
      
      // HVAC specific
      hvacCategory: eventData.category,
      requiredSkills: categoryConfig.skills,
      
      // Metadata
      createdBy: eventData.createdBy || 'system',
      createdAt: new Date()
    };

    // Auto-assign technician if not provided
    if (!serviceOrderData.assignedTechnician) {
      serviceOrderData.assignedTechnician = await findOptimalTechnician(
        eventData.district,
        eventData.category,
        new Date(eventData.scheduledDate)
      );
    }

    const serviceOrder = await ServiceOrder.create(serviceOrderData);
    
    // Transform to calendar event
    const calendarEvent = transformToCalendarEvent(serviceOrder);

    console.log(`✅ Created calendar event: ${serviceOrder._id}`);
    return calendarEvent;

  } catch (error) {
    console.error(`❌ Failed to create calendar event: ${error.message}`);
    throw error;
  }
};

// Update calendar event
const updateCalendarEvent = async (eventId, updateData) => {
  try {
    console.log(`📅 Updating calendar event: ${eventId}`);

    const serviceOrder = await ServiceOrder.findById(eventId);
    if (!serviceOrder) {
      throw new Error('Calendar event not found');
    }

    // Update fields
    const updates = {};
    
    if (updateData.title) updates.title = updateData.title;
    if (updateData.description) updates.description = updateData.description;
    if (updateData.scheduledDate) updates.scheduledDate = new Date(updateData.scheduledDate);
    if (updateData.duration) updates.estimatedDuration = updateData.duration;
    if (updateData.technicianId) updates.assignedTechnician = updateData.technicianId;
    if (updateData.status) updates.status = updateData.status;
    if (updateData.priority) updates.priority = updateData.priority;

    // Update location if provided
    if (updateData.address || updateData.district) {
      updates.location = {
        ...serviceOrder.location,
        ...(updateData.address && { address: updateData.address }),
        ...(updateData.district && { 
          district: updateData.district,
          coordinates: WARSAW_DISTRICTS[updateData.district] || null
        })
      };
    }

    updates.updatedAt = new Date();
    updates.updatedBy = updateData.updatedBy || 'system';

    const updatedServiceOrder = await ServiceOrder.findByIdAndUpdate(
      eventId,
      updates,
      { new: true }
    ).populate('client equipment assignedTechnician');

    const calendarEvent = transformToCalendarEvent(updatedServiceOrder);

    console.log(`✅ Updated calendar event: ${eventId}`);
    return calendarEvent;

  } catch (error) {
    console.error(`❌ Failed to update calendar event: ${error.message}`);
    throw error;
  }
};

// Delete calendar event
const deleteCalendarEvent = async (eventId) => {
  try {
    console.log(`📅 Deleting calendar event: ${eventId}`);

    const serviceOrder = await ServiceOrder.findById(eventId);
    if (!serviceOrder) {
      throw new Error('Calendar event not found');
    }

    // Soft delete - mark as cancelled
    await ServiceOrder.findByIdAndUpdate(eventId, {
      status: 'cancelled',
      cancelledAt: new Date(),
      updatedAt: new Date()
    });

    console.log(`✅ Deleted calendar event: ${eventId}`);
    return { success: true, eventId };

  } catch (error) {
    console.error(`❌ Failed to delete calendar event: ${error.message}`);
    throw error;
  }
};

/**
 * WARSAW OPTIMIZATION FUNCTIONS
 */

// Optimize routes for Warsaw districts
const optimizeWarsawRoutes = async (events, date) => {
  try {
    console.log(`🗺️ Optimizing Warsaw routes for ${date}`);

    // Group events by date and zone
    const eventsByZone = {};
    
    events.forEach(event => {
      const eventDate = moment(event.start).format('YYYY-MM-DD');
      const district = event.location?.district;
      const zone = WARSAW_DISTRICTS[district]?.zone || 'unknown';
      
      if (!eventsByZone[eventDate]) {
        eventsByZone[eventDate] = {};
      }
      if (!eventsByZone[eventDate][zone]) {
        eventsByZone[eventDate][zone] = [];
      }
      
      eventsByZone[eventDate][zone].push(event);
    });

    // Optimize each zone
    const optimizedEvents = [];
    
    for (const [date, zones] of Object.entries(eventsByZone)) {
      for (const [zone, zoneEvents] of Object.entries(zones)) {
        const optimized = optimizeZoneRoute(zoneEvents, zone);
        optimizedEvents.push(...optimized);
      }
    }

    console.log(`✅ Optimized ${optimizedEvents.length} events for Warsaw routes`);
    return optimizedEvents;

  } catch (error) {
    console.error(`❌ Route optimization failed: ${error.message}`);
    return events; // Return original events if optimization fails
  }
};

// Optimize route within a zone
const optimizeZoneRoute = (events, zone) => {
  // Sort by priority and time
  return events.sort((a, b) => {
    // Priority order: serwis > instalacja > oględziny
    const priorityOrder = { 'serwis': 3, 'instalacja': 2, 'oględziny': 1 };
    const aPriority = priorityOrder[a.category] || 0;
    const bPriority = priorityOrder[b.category] || 0;
    
    if (aPriority !== bPriority) {
      return bPriority - aPriority;
    }
    
    // Then by time
    return new Date(a.start) - new Date(b.start);
  });
};

// Find optimal technician for assignment
const findOptimalTechnician = async (district, category, scheduledDate) => {
  try {
    // This would integrate with a technician management system
    // For now, return null to indicate manual assignment needed
    return null;
  } catch (error) {
    console.error('Error finding optimal technician:', error);
    return null;
  }
};

/**
 * HELPER FUNCTIONS
 */

// Transform service order to calendar event
const transformToCalendarEvent = (serviceOrder) => {
  const categoryConfig = HVAC_CATEGORIES[serviceOrder.category] || HVAC_CATEGORIES['serwis'];

  return {
    id: serviceOrder._id,
    title: `${categoryConfig.icon} ${serviceOrder.title}`,
    start: serviceOrder.scheduledDate,
    end: new Date(serviceOrder.scheduledDate.getTime() + (serviceOrder.estimatedDuration || categoryConfig.duration) * 60000),

    // Calendar display
    backgroundColor: categoryConfig.color,
    borderColor: categoryConfig.color,
    textColor: '#ffffff',

    // Event data
    category: serviceOrder.category,
    categoryName: categoryConfig.name,
    priority: serviceOrder.priority,
    status: serviceOrder.status,

    // Client data
    client: serviceOrder.client ? {
      id: serviceOrder.client._id,
      name: serviceOrder.client.name,
      email: serviceOrder.client.email,
      phone: serviceOrder.client.phone,
      address: serviceOrder.client.address
    } : null,

    // Equipment data
    equipment: serviceOrder.equipment ? {
      id: serviceOrder.equipment._id,
      name: serviceOrder.equipment.name,
      brand: serviceOrder.equipment.brand,
      model: serviceOrder.equipment.model,
      serialNumber: serviceOrder.equipment.serialNumber
    } : null,

    // Technician data
    technician: serviceOrder.assignedTechnician ? {
      id: serviceOrder.assignedTechnician._id,
      name: serviceOrder.assignedTechnician.name,
      email: serviceOrder.assignedTechnician.email,
      phone: serviceOrder.assignedTechnician.phone,
      skills: serviceOrder.assignedTechnician.skills
    } : null,

    // Location data
    location: serviceOrder.location,

    // Additional data
    description: serviceOrder.description,
    estimatedDuration: serviceOrder.estimatedDuration || categoryConfig.duration,
    requiredSkills: serviceOrder.requiredSkills || categoryConfig.skills,

    // Metadata
    createdAt: serviceOrder.createdAt,
    updatedAt: serviceOrder.updatedAt
  };
};

// Get calendar statistics
const getCalendarStatistics = async (startDate, endDate) => {
  try {
    const query = {
      scheduledDate: {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      }
    };

    const [
      totalEvents,
      eventsByCategory,
      eventsByStatus,
      eventsByDistrict,
      averageDuration
    ] = await Promise.all([
      ServiceOrder.countDocuments(query),
      ServiceOrder.aggregate([
        { $match: query },
        { $group: { _id: '$category', count: { $sum: 1 } } }
      ]),
      ServiceOrder.aggregate([
        { $match: query },
        { $group: { _id: '$status', count: { $sum: 1 } } }
      ]),
      ServiceOrder.aggregate([
        { $match: query },
        { $group: { _id: '$location.district', count: { $sum: 1 } } }
      ]),
      ServiceOrder.aggregate([
        { $match: query },
        { $group: { _id: null, avgDuration: { $avg: '$estimatedDuration' } } }
      ])
    ]);

    return {
      totalEvents,
      eventsByCategory: eventsByCategory.reduce((acc, item) => {
        acc[item._id] = item.count;
        return acc;
      }, {}),
      eventsByStatus: eventsByStatus.reduce((acc, item) => {
        acc[item._id] = item.count;
        return acc;
      }, {}),
      eventsByDistrict: eventsByDistrict.reduce((acc, item) => {
        acc[item._id] = item.count;
        return acc;
      }, {}),
      averageDuration: averageDuration[0]?.avgDuration || 0
    };
  } catch (error) {
    console.error('Error getting calendar statistics:', error);
    throw error;
  }
};

// Get technician availability
const getTechnicianAvailability = async (technicianId, date) => {
  try {
    const startOfDay = moment(date).startOf('day').toDate();
    const endOfDay = moment(date).endOf('day').toDate();

    const bookedSlots = await ServiceOrder.find({
      assignedTechnician: technicianId,
      scheduledDate: {
        $gte: startOfDay,
        $lte: endOfDay
      },
      status: { $in: ['scheduled', 'in_progress'] }
    }).select('scheduledDate estimatedDuration');

    // Calculate available time slots (8:00 - 18:00)
    const workingHours = {
      start: moment(date).hour(8).minute(0),
      end: moment(date).hour(18).minute(0)
    };

    const availableSlots = [];
    let currentTime = workingHours.start.clone();

    while (currentTime.isBefore(workingHours.end)) {
      const slotEnd = currentTime.clone().add(60, 'minutes');

      // Check if slot is available
      const isBooked = bookedSlots.some(booking => {
        const bookingStart = moment(booking.scheduledDate);
        const bookingEnd = bookingStart.clone().add(booking.estimatedDuration || 120, 'minutes');

        return currentTime.isBetween(bookingStart, bookingEnd, null, '[)') ||
               slotEnd.isBetween(bookingStart, bookingEnd, null, '(]');
      });

      if (!isBooked) {
        availableSlots.push({
          start: currentTime.toDate(),
          end: slotEnd.toDate(),
          duration: 60
        });
      }

      currentTime.add(60, 'minutes');
    }

    return {
      date,
      technicianId,
      workingHours: {
        start: workingHours.start.toDate(),
        end: workingHours.end.toDate()
      },
      bookedSlots: bookedSlots.map(slot => ({
        start: slot.scheduledDate,
        duration: slot.estimatedDuration || 120
      })),
      availableSlots,
      totalAvailableHours: availableSlots.length
    };
  } catch (error) {
    console.error('Error getting technician availability:', error);
    throw error;
  }
};

// Suggest optimal time slots
const suggestOptimalTimeSlots = async (district, category, preferredDate) => {
  try {
    const categoryConfig = HVAC_CATEGORIES[category];
    const requiredDuration = categoryConfig.duration;

    // Get all technicians (would be filtered by skills in real implementation)
    const technicians = []; // Would fetch from technician model

    const suggestions = [];

    // Check next 7 days starting from preferred date
    for (let i = 0; i < 7; i++) {
      const checkDate = moment(preferredDate).add(i, 'days');

      // Skip weekends for now
      if (checkDate.day() === 0 || checkDate.day() === 6) continue;

      for (const technician of technicians) {
        const availability = await getTechnicianAvailability(technician._id, checkDate.toDate());

        // Find slots that can accommodate the required duration
        const suitableSlots = availability.availableSlots.filter(slot => {
          const slotDuration = moment(slot.end).diff(moment(slot.start), 'minutes');
          return slotDuration >= requiredDuration;
        });

        suitableSlots.forEach(slot => {
          suggestions.push({
            date: checkDate.format('YYYY-MM-DD'),
            time: moment(slot.start).format('HH:mm'),
            technician: {
              id: technician._id,
              name: technician.name
            },
            duration: requiredDuration,
            district,
            category,
            priority: calculateSlotPriority(slot, district, category, checkDate)
          });
        });
      }
    }

    // Sort by priority and return top suggestions
    return suggestions
      .sort((a, b) => b.priority - a.priority)
      .slice(0, 10);

  } catch (error) {
    console.error('Error suggesting optimal time slots:', error);
    throw error;
  }
};

// Calculate slot priority for optimization
const calculateSlotPriority = (slot, district, category, date) => {
  let priority = 50; // Base priority

  // Prefer morning slots for installations
  const hour = moment(slot.start).hour();
  if (category === 'instalacja' && hour >= 8 && hour <= 10) {
    priority += 20;
  }

  // Prefer afternoon slots for service calls
  if (category === 'serwis' && hour >= 13 && hour <= 16) {
    priority += 15;
  }

  // Prefer earlier dates
  const daysFromNow = moment(date).diff(moment(), 'days');
  priority -= daysFromNow * 2;

  return priority;
};

module.exports = {
  getCalendarEvents,
  createCalendarEvent,
  updateCalendarEvent,
  deleteCalendarEvent,
  optimizeWarsawRoutes,
  getCalendarStatistics,
  getTechnicianAvailability,
  suggestOptimalTimeSlots,
  transformToCalendarEvent,
  WARSAW_DISTRICTS,
  HVAC_CATEGORIES
};
