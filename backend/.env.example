# Database Configuration
DATABASE_URL=mongodb://localhost:27017/fulmark-hvac-crm

# JWT Secret
JWT_SECRET=your-super-secret-jwt-key-here

# OpenAI Configuration for AI Features
OPENAI_API_KEY=your-openai-api-key-here

# Email Configuration (if using email features)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-email-password

# Server Configuration
PORT=5000
NODE_ENV=development

# File Upload Configuration
MAX_FILE_SIZE=10485760

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Session Configuration
SESSION_SECRET=your-session-secret-here

# AWS S3 Configuration (if using S3 for file storage)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=eu-central-1
AWS_S3_BUCKET=your-s3-bucket-name

# Redis Configuration (if using Redis for caching)
REDIS_URL=redis://localhost:6379

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Gobeklitepe Semantic Framework Configuration
WEAVIATE_URL=http://localhost:8082
EMAIL_INTELLIGENCE_URL=http://localhost:8001

# External Services Configuration
MINIO_HOST=**************
MINIO_PORT=9000
MINIO_USERNAME=koldbringer
MINIO_PASSWORD=Blaeritipol1

MONGODB_EXTERNAL_HOST=**************
MONGODB_EXTERNAL_PORT=27017
MONGODB_EXTERNAL_USERNAME=Koldbringer
MONGODB_EXTERNAL_PASSWORD=blaeiritpol

REDIS_EXTERNAL_HOST=**************
REDIS_EXTERNAL_PORT=3037

# LM Studio Configuration
LM_STUDIO_URL=http://*************:1234
LM_STUDIO_MODEL=gemma-2-2b-it
