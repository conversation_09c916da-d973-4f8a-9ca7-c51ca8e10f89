/**
 * CUSTOMER INTERACTIONS DASHBOARD
 * Unified customer interactions data panel - emails + transcriptions + communication history
 * Part of HVAC CRM Email Intelligence System
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Button,
  Tag,
  Progress,
  Alert,
  Space,
  Tooltip,
  Typography,
  Spin,
  message,
  Modal,
  Select,
  DatePicker,
  Input,
  Timeline,
  Avatar,
  Divider,
  Badge
} from 'antd';
import {
  UserOutlined,
  MessageOutlined,
  PhoneOutlined,
  ToolOutlined,
  DollarOutlined,
  HeartOutlined,
  TrophyOutlined,
  WarningOutlined,
  ReloadOutlined,
  EyeOutlined,
  SearchOutlined,
  FilterOutlined,
  CalendarOutlined,
  FileTextOutlined,
  CustomerServiceOutlined
} from '@ant-design/icons';
import { Line, Column, Pie, Gauge } from '@ant-design/plots';
import { request } from '@/request';
import { formatCurrency, formatDate, formatDateTime } from '@/utils/helpers';

const { Title, Text, Paragraph } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { Search } = Input;

const CustomerInteractionsDashboard = () => {
  const [loading, setLoading] = useState(true);
  const [healthDashboard, setHealthDashboard] = useState(null);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [customerDetails, setCustomerDetails] = useState(null);
  const [detailsModalVisible, setDetailsModalVisible] = useState(false);
  const [filters, setFilters] = useState({
    healthStatus: 'all',
    search: ''
  });
  const [overviewStats, setOverviewStats] = useState(null);

  // Load health dashboard data
  const loadHealthDashboard = async () => {
    try {
      setLoading(true);
      const response = await request.get('/api/customer-interactions/health/dashboard');
      if (response.success) {
        setHealthDashboard(response.result);
      }
    } catch (error) {
      message.error('Błąd podczas ładowania dashboard zdrowia klientów');
      console.error('Health dashboard load error:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load overview statistics
  const loadOverviewStats = async () => {
    try {
      const response = await request.get('/api/customer-interactions/stats/overview');
      if (response.success) {
        setOverviewStats(response.result);
      }
    } catch (error) {
      console.error('Overview stats load error:', error);
    }
  };

  // Load customer details
  const loadCustomerDetails = async (customerId) => {
    try {
      const response = await request.get(`/api/customer-interactions/${customerId}`);
      if (response.success) {
        setCustomerDetails(response.result);
        setDetailsModalVisible(true);
      }
    } catch (error) {
      message.error('Błąd podczas ładowania szczegółów klienta');
      console.error('Customer details load error:', error);
    }
  };

  useEffect(() => {
    loadHealthDashboard();
    loadOverviewStats();
  }, []);

  // Filter customers based on filters
  const getFilteredCustomers = () => {
    if (!healthDashboard?.customers) return [];

    let filtered = healthDashboard.customers;

    if (filters.healthStatus !== 'all') {
      filtered = filtered.filter(customer => {
        const status = getHealthStatus(customer.healthScore);
        return status === filters.healthStatus;
      });
    }

    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(customer =>
        customer.customerName?.toLowerCase().includes(searchLower) ||
        customer.customerEmail?.toLowerCase().includes(searchLower)
      );
    }

    return filtered;
  };

  // Get health status
  const getHealthStatus = (score) => {
    if (score >= 80) return 'excellent';
    if (score >= 70) return 'good';
    if (score >= 50) return 'fair';
    if (score >= 30) return 'poor';
    return 'critical';
  };

  // Get health status color
  const getHealthStatusColor = (status) => {
    const colors = {
      'excellent': '#52c41a',
      'good': '#1890ff',
      'fair': '#faad14',
      'poor': '#ff7a45',
      'critical': '#ff4d4f'
    };
    return colors[status] || '#d9d9d9';
  };

  // Get health status label
  const getHealthStatusLabel = (status) => {
    const labels = {
      'excellent': 'Doskonały',
      'good': 'Dobry',
      'fair': 'Przeciętny',
      'poor': 'Słaby',
      'critical': 'Krytyczny'
    };
    return labels[status] || 'Nieznany';
  };

  // Render overview statistics
  const renderOverviewStats = () => {
    if (!overviewStats) return null;

    return (
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Łączna liczba klientów"
              value={overviewStats.totalCustomers}
              prefix={<UserOutlined style={{ color: '#1890ff' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Aktywni klienci (30 dni)"
              value={overviewStats.activeCustomers}
              prefix={<TrophyOutlined style={{ color: '#52c41a' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Łączne interakcje"
              value={overviewStats.totalEmails + overviewStats.totalServiceOrders}
              prefix={<MessageOutlined style={{ color: '#722ed1' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Wskaźnik aktywności"
              value={overviewStats.activityRate}
              suffix="%"
              precision={1}
              prefix={<HeartOutlined style={{ color: '#eb2f96' }} />}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  // Render health distribution chart
  const renderHealthDistribution = () => {
    if (!healthDashboard?.statistics) return null;

    const data = [
      { status: 'Zdrowi (≥70)', count: healthDashboard.statistics.healthyCustomers, color: '#52c41a' },
      { status: 'Zagrożeni (<50)', count: healthDashboard.statistics.atRiskCustomers, color: '#ff4d4f' },
      { status: 'Pozostali', count: healthDashboard.statistics.totalCustomers - healthDashboard.statistics.healthyCustomers - healthDashboard.statistics.atRiskCustomers, color: '#faad14' }
    ];

    const config = {
      data,
      angleField: 'count',
      colorField: 'status',
      radius: 0.8,
      label: {
        type: 'outer',
        content: '{name} ({percentage})'
      },
      color: data.map(item => item.color)
    };

    return (
      <Card title="Rozkład zdrowia klientów" extra={
        <Tooltip title="Rozkład klientów według wskaźnika zdrowia">
          <WarningOutlined />
        </Tooltip>
      }>
        <Pie {...config} height={300} />
      </Card>
    );
  };

  // Render customers table
  const renderCustomersTable = () => {
    const filteredCustomers = getFilteredCustomers();

    const columns = [
      {
        title: 'Klient',
        key: 'customer',
        render: (_, record) => (
          <Space>
            <Avatar icon={<UserOutlined />} />
            <div>
              <div style={{ fontWeight: 'bold' }}>{record.customerName}</div>
              <div style={{ color: '#666', fontSize: '12px' }}>{record.customerEmail}</div>
            </div>
          </Space>
        )
      },
      {
        title: 'Wskaźnik zdrowia',
        dataIndex: 'healthScore',
        key: 'healthScore',
        render: (score) => {
          const status = getHealthStatus(score);
          return (
            <Space>
              <Progress
                type="circle"
                size={50}
                percent={score || 0}
                strokeColor={getHealthStatusColor(status)}
                format={() => `${score || 0}`}
              />
              <Tag color={getHealthStatusColor(status)}>
                {getHealthStatusLabel(status)}
              </Tag>
            </Space>
          );
        },
        sorter: (a, b) => (a.healthScore || 0) - (b.healthScore || 0)
      },
      {
        title: 'Interakcje',
        key: 'interactions',
        render: (_, record) => (
          <Space direction="vertical" size="small">
            <div>
              <MessageOutlined /> Emaile: {record.emailCount || 0}
            </div>
            <div>
              <ToolOutlined /> Serwis: {record.serviceCount || 0}
            </div>
            <div>
              <DollarOutlined /> Sprzedaż: {record.opportunityCount || 0}
            </div>
          </Space>
        )
      },
      {
        title: 'Ostatnia interakcja',
        dataIndex: 'lastInteraction',
        key: 'lastInteraction',
        render: (date) => date ? formatDateTime(date) : 'Brak danych',
        sorter: (a, b) => {
          const dateA = a.lastInteraction ? new Date(a.lastInteraction) : new Date(0);
          const dateB = b.lastInteraction ? new Date(b.lastInteraction) : new Date(0);
          return dateB - dateA;
        }
      },
      {
        title: 'Akcje',
        key: 'actions',
        render: (_, record) => (
          <Space>
            <Button
              type="primary"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => loadCustomerDetails(record.customerId)}
            >
              Szczegóły
            </Button>
          </Space>
        )
      }
    ];

    return (
      <Card
        title="Klienci - Analiza Interakcji"
        extra={
          <Space>
            <Search
              placeholder="Szukaj klienta..."
              value={filters.search}
              onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              style={{ width: 200 }}
            />
            <Select
              value={filters.healthStatus}
              onChange={(value) => setFilters({ ...filters, healthStatus: value })}
              style={{ width: 150 }}
            >
              <Option value="all">Wszyscy</Option>
              <Option value="excellent">Doskonali</Option>
              <Option value="good">Dobrzy</Option>
              <Option value="fair">Przeciętni</Option>
              <Option value="poor">Słabi</Option>
              <Option value="critical">Krytyczni</Option>
            </Select>
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              onClick={loadHealthDashboard}
            >
              Odśwież
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={filteredCustomers}
          rowKey="customerId"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} z ${total} klientów`
          }}
        />
      </Card>
    );
  };

  // Render customer details modal
  const renderCustomerDetailsModal = () => {
    if (!customerDetails) return null;

    const { customer, timeline, metrics, aiInsights, summary } = customerDetails;

    return (
      <Modal
        title={
          <Space>
            <Avatar icon={<UserOutlined />} />
            <div>
              <div style={{ fontWeight: 'bold' }}>{customer.name}</div>
              <div style={{ color: '#666', fontSize: '12px' }}>{customer.email}</div>
            </div>
          </Space>
        }
        visible={detailsModalVisible}
        onCancel={() => setDetailsModalVisible(false)}
        footer={null}
        width={1000}
      >
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Card size="small">
              <Row gutter={16}>
                <Col span={6}>
                  <Statistic
                    title="Wskaźnik zdrowia"
                    value={customer.healthScore || 0}
                    suffix="/100"
                    valueStyle={{ color: getHealthStatusColor(getHealthStatus(customer.healthScore)) }}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title="Łączne interakcje"
                    value={customer.totalInteractions || 0}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title="Ostatnie 30 dni"
                    value={metrics?.last30Days || 0}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title="Łączna wartość"
                    value={summary?.totalValue || 0}
                    formatter={(value) => formatCurrency(value)}
                  />
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>

        <Divider>Timeline Interakcji</Divider>

        <div style={{ maxHeight: 400, overflowY: 'auto' }}>
          <Timeline>
            {timeline?.slice(0, 20).map((item, index) => (
              <Timeline.Item
                key={index}
                color={getTimelineColor(item.type)}
                dot={getTimelineIcon(item.type)}
              >
                <div>
                  <div style={{ fontWeight: 'bold' }}>{item.title}</div>
                  <div style={{ color: '#666', fontSize: '12px' }}>
                    {formatDateTime(item.timestamp)}
                  </div>
                  <div style={{ marginTop: 4 }}>{item.description}</div>
                  {item.priority === 'high' && (
                    <Tag color="red" size="small" style={{ marginTop: 4 }}>
                      Wysoki priorytet
                    </Tag>
                  )}
                </div>
              </Timeline.Item>
            ))}
          </Timeline>
        </div>

        {aiInsights && (
          <>
            <Divider>AI Insights</Divider>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Card size="small" title="Wzorzec komunikacji">
                  <Tag color="blue">{aiInsights.communicationPattern}</Tag>
                </Card>
              </Col>
              <Col span={12}>
                <Card size="small" title="Trend sentymentu">
                  <Tag color={aiInsights.sentimentTrend === 'positive' ? 'green' : aiInsights.sentimentTrend === 'negative' ? 'red' : 'default'}>
                    {aiInsights.sentimentTrend}
                  </Tag>
                </Card>
              </Col>
            </Row>
          </>
        )}
      </Modal>
    );
  };

  // Helper functions for timeline
  const getTimelineColor = (type) => {
    const colors = {
      'email': 'blue',
      'transcription': 'purple',
      'service': 'orange',
      'sales': 'green',
      'invoice': 'red'
    };
    return colors[type] || 'gray';
  };

  const getTimelineIcon = (type) => {
    const icons = {
      'email': <MessageOutlined />,
      'transcription': <PhoneOutlined />,
      'service': <ToolOutlined />,
      'sales': <TrophyOutlined />,
      'invoice': <FileTextOutlined />
    };
    return icons[type] || <UserOutlined />;
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>
          <Text>Ładowanie interakcji klientów...</Text>
        </div>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>
          <CustomerServiceOutlined /> Panel Interakcji Klientów
        </Title>
        <Text type="secondary">
          Zunifikowany widok wszystkich interakcji klientów - emaile, transkrypcje, historia komunikacji
        </Text>
      </div>

      {/* Overview Statistics */}
      {renderOverviewStats()}

      {/* Charts and Health Dashboard */}
      <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
        <Col xs={24} lg={12}>
          {renderHealthDistribution()}
        </Col>
        <Col xs={24} lg={12}>
          <Card title="Średni wskaźnik zdrowia">
            <div style={{ textAlign: 'center' }}>
              <Progress
                type="dashboard"
                percent={healthDashboard?.statistics?.averageHealthScore || 0}
                strokeColor={{
                  '0%': '#ff4d4f',
                  '50%': '#faad14',
                  '100%': '#52c41a'
                }}
                format={(percent) => `${percent.toFixed(1)}`}
              />
            </div>
          </Card>
        </Col>
      </Row>

      {/* Customers Table */}
      <div style={{ marginTop: 24 }}>
        {renderCustomersTable()}
      </div>

      {/* Customer Details Modal */}
      {renderCustomerDetailsModal()}
    </div>
  );
};

export default CustomerInteractionsDashboard;
