/**
 * INVOICE ANALYSIS DASHBOARD
 * AI-powered invoice analysis from email attachments
 * Part of HVAC CRM Email Intelligence System
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Button,
  Tag,
  Progress,
  Alert,
  Space,
  Tooltip,
  Typography,
  Spin,
  message,
  Modal,
  Select,
  DatePicker,
  Input
} from 'antd';
import {
  FileTextOutlined,
  DollarOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  ReloadOutlined,
  EyeOutlined,
  SearchOutlined,
  FilterOutlined
} from '@ant-design/icons';
import { Line, Column, Pie } from '@ant-design/plots';
import { request } from '@/request';
import { formatCurrency, formatDate } from '@/utils/helpers';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

const InvoiceAnalysisDashboard = () => {
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [analyzedInvoices, setAnalyzedInvoices] = useState([]);
  const [pendingAttachments, setPendingAttachments] = useState([]);
  const [filters, setFilters] = useState({
    category: 'all',
    dateRange: null,
    search: ''
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [batchAnalyzing, setBatchAnalyzing] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  const [detailsModalVisible, setDetailsModalVisible] = useState(false);

  // Load dashboard data
  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const response = await request.get('/api/invoice-analysis/dashboard');
      if (response.success) {
        setDashboardData(response.result);
      }
    } catch (error) {
      message.error('Błąd podczas ładowania danych dashboard');
      console.error('Dashboard load error:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load analyzed invoices
  const loadAnalyzedInvoices = async (page = 1) => {
    try {
      const params = {
        page,
        limit: pagination.pageSize,
        ...filters
      };

      const response = await request.get('/api/invoice-analysis/invoices', { params });
      if (response.success) {
        setAnalyzedInvoices(response.result.invoices);
        setPagination({
          ...pagination,
          current: page,
          total: response.result.pagination.total
        });
      }
    } catch (error) {
      message.error('Błąd podczas ładowania faktur');
      console.error('Invoices load error:', error);
    }
  };

  // Load pending attachments
  const loadPendingAttachments = async () => {
    try {
      const response = await request.get('/api/invoice-analysis/pending');
      if (response.success) {
        setPendingAttachments(response.result.pendingInvoices);
      }
    } catch (error) {
      message.error('Błąd podczas ładowania oczekujących załączników');
      console.error('Pending attachments load error:', error);
    }
  };

  // Batch analyze invoices
  const handleBatchAnalyze = async () => {
    try {
      setBatchAnalyzing(true);
      const response = await request.post('/api/invoice-analysis/batch-analyze');
      if (response.success) {
        message.success(`Przeanalizowano ${response.result.totalProcessed} faktur`);
        loadDashboardData();
        loadAnalyzedInvoices();
        loadPendingAttachments();
      }
    } catch (error) {
      message.error('Błąd podczas analizy wsadowej');
      console.error('Batch analyze error:', error);
    } finally {
      setBatchAnalyzing(false);
    }
  };

  // Analyze single attachment
  const handleAnalyzeSingle = async (emailId, attachmentId) => {
    try {
      const response = await request.post(`/api/invoice-analysis/analyze/${emailId}/${attachmentId}`);
      if (response.success) {
        message.success('Faktura przeanalizowana pomyślnie');
        loadDashboardData();
        loadAnalyzedInvoices();
        loadPendingAttachments();
      }
    } catch (error) {
      message.error('Błąd podczas analizy faktury');
      console.error('Single analyze error:', error);
    }
  };

  // View invoice details
  const handleViewDetails = async (invoiceId) => {
    try {
      const response = await request.get(`/api/invoice-analysis/invoices/${invoiceId}`);
      if (response.success) {
        setSelectedInvoice(response.result);
        setDetailsModalVisible(true);
      }
    } catch (error) {
      message.error('Błąd podczas ładowania szczegółów faktury');
      console.error('Invoice details error:', error);
    }
  };

  useEffect(() => {
    loadDashboardData();
    loadAnalyzedInvoices();
    loadPendingAttachments();
  }, []);

  useEffect(() => {
    loadAnalyzedInvoices(1);
  }, [filters]);

  // Statistics cards
  const renderStatistics = () => {
    if (!dashboardData) return null;

    return (
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Przeanalizowane faktury"
              value={dashboardData.totalAnalyzed}
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Oczekujące analizy"
              value={dashboardData.pendingAnalysis}
              prefix={<ClockCircleOutlined style={{ color: '#faad14' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Łączna wartość"
              value={dashboardData.byCategory?.reduce((sum, cat) => sum + (cat.totalAmount || 0), 0) || 0}
              prefix={<DollarOutlined style={{ color: '#1890ff' }} />}
              formatter={(value) => formatCurrency(value)}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Średnia pewność AI"
              value={dashboardData.averageConfidence?.[0]?.avgConfidence || 0}
              suffix="%"
              precision={1}
              prefix={<FileTextOutlined style={{ color: '#722ed1' }} />}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  // Category distribution chart
  const renderCategoryChart = () => {
    if (!dashboardData?.byCategory) return null;

    const data = dashboardData.byCategory.map(cat => ({
      category: cat._id || 'Inne',
      count: cat.count,
      value: cat.totalAmount || 0
    }));

    const config = {
      data,
      angleField: 'count',
      colorField: 'category',
      radius: 0.8,
      label: {
        type: 'outer',
        content: '{name} ({percentage})'
      },
      interactions: [{ type: 'element-active' }]
    };

    return (
      <Card title="Rozkład kategorii HVAC" extra={
        <Tooltip title="Rozkład faktur według kategorii HVAC">
          <WarningOutlined />
        </Tooltip>
      }>
        <Pie {...config} height={300} />
      </Card>
    );
  };

  // Monthly trend chart
  const renderMonthlyChart = () => {
    if (!dashboardData?.byMonth) return null;

    const data = dashboardData.byMonth.map(month => ({
      month: `${month._id.year}-${month._id.month.toString().padStart(2, '0')}`,
      count: month.count,
      value: month.totalAmount || 0
    })).reverse();

    const config = {
      data,
      xField: 'month',
      yField: 'count',
      point: {
        size: 5,
        shape: 'diamond'
      },
      label: {
        style: {
          fill: '#aaa'
        }
      }
    };

    return (
      <Card title="Trend miesięczny" extra={
        <Tooltip title="Liczba przeanalizowanych faktur w czasie">
          <WarningOutlined />
        </Tooltip>
      }>
        <Line {...config} height={300} />
      </Card>
    );
  };

  // Analyzed invoices table
  const renderInvoicesTable = () => {
    const columns = [
      {
        title: 'Numer faktury',
        dataIndex: 'number',
        key: 'number',
        render: (text, record) => (
          <Button type="link" onClick={() => handleViewDetails(record._id)}>
            {text}
          </Button>
        )
      },
      {
        title: 'Data',
        dataIndex: 'date',
        key: 'date',
        render: (date) => formatDate(date)
      },
      {
        title: 'Wartość',
        dataIndex: 'total',
        key: 'total',
        render: (value) => formatCurrency(value),
        sorter: true
      },
      {
        title: 'Kategoria HVAC',
        dataIndex: 'hvacCategory',
        key: 'hvacCategory',
        render: (category) => (
          <Tag color={getCategoryColor(category)}>
            {category || 'Inne'}
          </Tag>
        )
      },
      {
        title: 'Marki sprzętu',
        dataIndex: 'equipmentBrands',
        key: 'equipmentBrands',
        render: (brands) => (
          <Space>
            {(brands || []).slice(0, 2).map(brand => (
              <Tag key={brand} size="small">{brand}</Tag>
            ))}
            {brands?.length > 2 && <Tag size="small">+{brands.length - 2}</Tag>}
          </Space>
        )
      },
      {
        title: 'Pewność AI',
        dataIndex: ['analysisData', 'confidence_score'],
        key: 'confidence',
        render: (confidence) => (
          <Progress
            percent={Math.round((confidence || 0) * 100)}
            size="small"
            status={confidence > 0.8 ? 'success' : confidence > 0.6 ? 'normal' : 'exception'}
          />
        )
      },
      {
        title: 'Akcje',
        key: 'actions',
        render: (_, record) => (
          <Space>
            <Button
              type="primary"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetails(record._id)}
            >
              Szczegóły
            </Button>
          </Space>
        )
      }
    ];

    return (
      <Card
        title="Przeanalizowane faktury"
        extra={
          <Space>
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              onClick={() => loadAnalyzedInvoices()}
            >
              Odśwież
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={analyzedInvoices}
          rowKey="_id"
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} z ${total} faktur`
          }}
          onChange={(pag) => loadAnalyzedInvoices(pag.current)}
        />
      </Card>
    );
  };

  // Helper function for category colors
  const getCategoryColor = (category) => {
    const colors = {
      'klimatyzacja': 'blue',
      'wentylacja': 'green',
      'pompa_ciepla': 'orange',
      'serwis': 'purple',
      'inne': 'default'
    };
    return colors[category] || 'default';
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>
          <Text>Ładowanie analizy faktur...</Text>
        </div>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>
          <FileTextOutlined /> Analiza Faktur AI
        </Title>
        <Text type="secondary">
          Automatyczna analiza faktur z załączników email przy użyciu sztucznej inteligencji
        </Text>
      </div>

      {/* System Health Alert */}
      {dashboardData?.systemHealth && (
        <Alert
          message="System analizy faktur działa prawidłowo"
          description={`Ostatnia analiza: ${formatDate(dashboardData.systemHealth.lastAnalysis)}`}
          type="success"
          showIcon
          style={{ marginBottom: 24 }}
          action={
            <Button
              size="small"
              type="primary"
              icon={<ReloadOutlined />}
              loading={batchAnalyzing}
              onClick={handleBatchAnalyze}
            >
              Analizuj wsadowo
            </Button>
          }
        />
      )}

      {/* Statistics */}
      {renderStatistics()}

      {/* Charts */}
      <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
        <Col xs={24} lg={12}>
          {renderCategoryChart()}
        </Col>
        <Col xs={24} lg={12}>
          {renderMonthlyChart()}
        </Col>
      </Row>

      {/* Invoices Table */}
      <div style={{ marginTop: 24 }}>
        {renderInvoicesTable()}
      </div>

      {/* Invoice Details Modal */}
      <Modal
        title="Szczegóły analizy faktury"
        visible={detailsModalVisible}
        onCancel={() => setDetailsModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedInvoice && (
          <div>
            {/* Invoice details content will be added here */}
            <Text>Szczegóły faktury: {selectedInvoice.invoice?.number}</Text>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default InvoiceAnalysisDashboard;
