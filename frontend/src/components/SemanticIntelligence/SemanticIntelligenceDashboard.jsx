import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Timeline, 
  Badge, 
  Button, 
  Statistic, 
  Progress,
  List,
  Avatar,
  Tag,
  Spin,
  Alert,
  Typography,
  Input,
  Select,
  Space
} from 'antd';
import {
  BrainCircuit,
  Database,
  Search,
  TrendingUp,
  Users,
  Clock,
  CheckCircle,
  AlertTriangle,
  Zap,
  Eye,
  MessageSquare,
  Target
} from 'lucide-react';

const { Title, Text } = Typography;
const { Search: SearchInput } = Input;
const { Option } = Select;

const SemanticIntelligenceDashboard = () => {
  const [insights, setInsights] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [systemHealth, setSystemHealth] = useState({});
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);

  useEffect(() => {
    fetchSystemHealth();
    
    // Refresh every 30 seconds
    const interval = setInterval(() => {
      fetchSystemHealth();
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const fetchSystemHealth = async () => {
    try {
      const response = await fetch('/api/semantic/health');
      if (!response.ok) throw new Error('Failed to fetch health status');
      const data = await response.json();
      setSystemHealth(data.result);
      setError(null);
    } catch (error) {
      console.error('Error fetching system health:', error);
      setError('Failed to connect to Semantic Intelligence service');
    } finally {
      setLoading(false);
    }
  };

  const fetchCustomerInsights = async (customerId) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/semantic/insights/${customerId}`);
      if (!response.ok) throw new Error('Failed to fetch customer insights');
      const data = await response.json();
      setInsights(data.result);
      setSelectedCustomer(customerId);
      setError(null);
    } catch (error) {
      console.error('Error fetching customer insights:', error);
      setError('Failed to fetch customer insights');
    } finally {
      setLoading(false);
    }
  };

  const performSemanticSearch = async (query) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      // This would be implemented when we have the search endpoint
      console.log('Performing semantic search for:', query);
      // Placeholder results
      setSearchResults([
        {
          id: '1',
          type: 'customer',
          title: 'Biuro Rachunkowe ABC',
          description: 'Customer with recent HVAC service requests',
          relevance: 0.95
        },
        {
          id: '2',
          type: 'interaction',
          title: 'Service Call - Klimatyzacja LG',
          description: 'Recent service interaction about LG air conditioning',
          relevance: 0.87
        }
      ]);
    } catch (error) {
      console.error('Error performing semantic search:', error);
    }
  };

  const getHealthColor = (isHealthy) => {
    return isHealthy ? 'success' : 'error';
  };

  const getHealthIcon = (isHealthy) => {
    return isHealthy ? <CheckCircle size={16} /> : <AlertTriangle size={16} />;
  };

  if (error && !systemHealth.weaviate) {
    return (
      <Alert
        message="Semantic Intelligence Service Unavailable"
        description={error}
        type="warning"
        showIcon
        action={
          <Button size="small" onClick={fetchSystemHealth}>
            Retry
          </Button>
        }
      />
    );
  }

  return (
    <div className="semantic-intelligence-dashboard">
      <Row gutter={[16, 16]}>
        {/* Header */}
        <Col span={24}>
          <Card>
            <Row justify="space-between" align="middle">
              <Col>
                <Title level={3} style={{ margin: 0 }}>
                  🧠 Semantic Intelligence Dashboard
                </Title>
                <Text type="secondary">
                  Advanced customer profiling and interaction analysis
                </Text>
              </Col>
              <Col>
                <Space>
                  <Badge 
                    status={getHealthColor(systemHealth.weaviate)} 
                    text="Weaviate Vector DB" 
                  />
                  <Badge 
                    status={getHealthColor(systemHealth.connected)} 
                    text="Semantic Framework" 
                  />
                </Space>
              </Col>
            </Row>
          </Card>
        </Col>

        {/* Semantic Search */}
        <Col span={24}>
          <Card title="🔍 Semantic Search" size="small">
            <Row gutter={16}>
              <Col span={18}>
                <SearchInput
                  placeholder="Search customers, interactions, or insights using natural language..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onSearch={performSemanticSearch}
                  enterButton={<Search size={16} />}
                  size="large"
                />
              </Col>
              <Col span={6}>
                <Select
                  placeholder="Search Type"
                  style={{ width: '100%' }}
                  size="large"
                >
                  <Option value="all">All Content</Option>
                  <Option value="customers">Customers</Option>
                  <Option value="interactions">Interactions</Option>
                  <Option value="insights">AI Insights</Option>
                </Select>
              </Col>
            </Row>
            
            {searchResults.length > 0 && (
              <div style={{ marginTop: 16 }}>
                <List
                  dataSource={searchResults}
                  renderItem={(item) => (
                    <List.Item
                      actions={[
                        <Button 
                          type="link" 
                          icon={<Eye size={16} />}
                          onClick={() => item.type === 'customer' && fetchCustomerInsights(item.id)}
                        >
                          View
                        </Button>
                      ]}
                    >
                      <List.Item.Meta
                        avatar={
                          <Avatar 
                            style={{ backgroundColor: '#1890ff' }}
                            icon={item.type === 'customer' ? <Users size={16} /> : <MessageSquare size={16} />}
                          />
                        }
                        title={
                          <div>
                            <Text strong>{item.title}</Text>
                            <Tag color="blue" style={{ marginLeft: 8 }}>
                              {Math.round(item.relevance * 100)}% match
                            </Tag>
                          </div>
                        }
                        description={item.description}
                      />
                    </List.Item>
                  )}
                />
              </div>
            )}
          </Card>
        </Col>

        {/* Customer Insights */}
        {selectedCustomer && insights.profile && (
          <>
            <Col span={24}>
              <Card title={`👤 Customer Profile: ${insights.profile.companyName}`} loading={loading}>
                <Row gutter={16}>
                  <Col span={6}>
                    <Card size="small" className="metric-card">
                      <Statistic
                        title="Total Interactions"
                        value={insights.insights?.totalInteractions || 0}
                        prefix={<MessageSquare size={20} />}
                        valueStyle={{ color: '#1890ff' }}
                      />
                    </Card>
                  </Col>
                  <Col span={6}>
                    <Card size="small" className="metric-card">
                      <Statistic
                        title="Sentiment Trend"
                        value={insights.insights?.sentimentTrend || 'neutral'}
                        prefix={<TrendingUp size={20} />}
                        valueStyle={{ 
                          color: insights.insights?.sentimentTrend === 'improving' ? '#52c41a' : 
                                 insights.insights?.sentimentTrend === 'declining' ? '#ff4d4f' : '#faad14'
                        }}
                      />
                    </Card>
                  </Col>
                  <Col span={6}>
                    <Card size="small" className="metric-card">
                      <Statistic
                        title="Business Type"
                        value={insights.profile.businessType || 'unknown'}
                        prefix={<Target size={20} />}
                        valueStyle={{ color: '#722ed1' }}
                      />
                    </Card>
                  </Col>
                  <Col span={6}>
                    <Card size="small" className="metric-card">
                      <Statistic
                        title="Building Size"
                        value={insights.profile.buildingSize || 0}
                        suffix="m²"
                        prefix={<Database size={20} />}
                        valueStyle={{ color: '#fa8c16' }}
                      />
                    </Card>
                  </Col>
                </Row>
              </Card>
            </Col>

            {/* Recent Interactions */}
            <Col span={12}>
              <Card title="💬 Recent Interactions" loading={loading}>
                <Timeline>
                  {insights.recentInteractions?.map((interaction, index) => (
                    <Timeline.Item 
                      key={index}
                      dot={<MessageSquare size={16} />}
                      color={
                        interaction.sentiment === 'positive' ? 'green' :
                        interaction.sentiment === 'negative' ? 'red' : 'blue'
                      }
                    >
                      <div className="timeline-item">
                        <div className="interaction-header">
                          <Text strong>{interaction.type}</Text>
                          <Tag color={
                            interaction.sentiment === 'positive' ? 'green' :
                            interaction.sentiment === 'negative' ? 'red' : 'blue'
                          }>
                            {interaction.sentiment}
                          </Tag>
                        </div>
                        <div className="interaction-content">
                          <Text>{interaction.content?.substring(0, 100)}...</Text>
                        </div>
                        <div className="interaction-time">
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            {new Date(interaction.timestamp).toLocaleString()}
                          </Text>
                        </div>
                      </div>
                    </Timeline.Item>
                  )) || (
                    <Timeline.Item>
                      <Text type="secondary">No recent interactions</Text>
                    </Timeline.Item>
                  )}
                </Timeline>
              </Card>
            </Col>

            {/* Top Topics & Action Items */}
            <Col span={12}>
              <Card title="🏷️ Topics & Actions" loading={loading}>
                <div style={{ marginBottom: 16 }}>
                  <Text strong>Top Topics:</Text>
                  <div style={{ marginTop: 8 }}>
                    {insights.insights?.topTopics?.map((topic, index) => (
                      <Tag key={index} color="blue" style={{ margin: '2px' }}>
                        {topic.topic} ({topic.count})
                      </Tag>
                    )) || <Text type="secondary">No topics identified</Text>}
                  </div>
                </div>
                
                <div>
                  <Text strong>Action Items:</Text>
                  <List
                    size="small"
                    dataSource={insights.insights?.actionItems || []}
                    renderItem={(item, index) => (
                      <List.Item key={index}>
                        <Text>{item}</Text>
                      </List.Item>
                    )}
                    locale={{ emptyText: 'No action items' }}
                  />
                </div>
              </Card>
            </Col>
          </>
        )}

        {/* System Status */}
        <Col span={24}>
          <Card title="🔧 System Status" size="small">
            <Row gutter={16}>
              <Col span={8}>
                <Text>Weaviate Vector Database:</Text>
                <Progress 
                  percent={systemHealth.weaviate ? 100 : 0} 
                  size="small" 
                  status={systemHealth.weaviate ? "success" : "exception"}
                  format={() => systemHealth.weaviate ? 'Connected' : 'Disconnected'}
                />
              </Col>
              <Col span={8}>
                <Text>Semantic Framework:</Text>
                <Progress 
                  percent={systemHealth.connected ? 100 : 0} 
                  size="small" 
                  status={systemHealth.connected ? "success" : "exception"}
                  format={() => systemHealth.connected ? 'Active' : 'Inactive'}
                />
              </Col>
              <Col span={8}>
                <Text>Integration Status:</Text>
                <Progress 
                  percent={85} 
                  size="small" 
                  status="active"
                  format={() => 'Operational'}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      <style jsx>{`
        .semantic-intelligence-dashboard .metric-card {
          text-align: center;
          border: 1px solid #f0f0f0;
        }
        
        .timeline-item {
          margin-bottom: 8px;
        }
        
        .interaction-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 4px;
        }
        
        .interaction-content {
          margin-bottom: 4px;
        }
      `}</style>
    </div>
  );
};

export default SemanticIntelligenceDashboard;
