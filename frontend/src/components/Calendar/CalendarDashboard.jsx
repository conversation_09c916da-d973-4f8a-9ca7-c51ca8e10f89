/**
 * CALENDAR DASHBOARD
 * Enhanced calendar integration with 3-category HVAC system and Warsaw optimization
 * Categories: 🔧 <PERSON><PERSON><PERSON>, 🏗️ Instalacja, 🔍 Oględziny
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Button,
  Tag,
  Alert,
  Space,
  Tooltip,
  Typography,
  Spin,
  message,
  Modal,
  Select,
  DatePicker,
  Calendar,
  Badge,
  Divider,
  Timeline,
  Avatar
} from 'antd';
import {
  CalendarOutlined,
  ToolOutlined,
  HomeOutlined,
  SearchOutlined,
  EnvironmentOutlined,
  ClockCircleOutlined,
  UserOutlined,
  ReloadOutlined,
  PlusOutlined,
  OptimizationOutlined,
  WarningOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import { Column, Pie, Line } from '@ant-design/plots';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import { request } from '@/request';
import { formatDate, formatDateTime } from '@/utils/helpers';
import moment from 'moment';

const { Title, Text } = Typography;
const { Option } = Select;

const CalendarDashboard = () => {
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [calendarEvents, setCalendarEvents] = useState([]);
  const [selectedDate, setSelectedDate] = useState(moment().format('YYYY-MM-DD'));
  const [filters, setFilters] = useState({
    category: 'all',
    district: 'all',
    status: 'all'
  });
  const [createEventModalVisible, setCreateEventModalVisible] = useState(false);
  const [optimizationModalVisible, setOptimizationModalVisible] = useState(false);
  const [optimizedRoutes, setOptimizedRoutes] = useState(null);
  const [hvacCategories, setHvacCategories] = useState([]);
  const [warsawDistricts, setWarsawDistricts] = useState([]);

  // Load dashboard data
  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const response = await request.get('/api/calendar/dashboard');
      if (response.success) {
        setDashboardData(response.result);
      }
    } catch (error) {
      message.error('Błąd podczas ładowania dashboard kalendarza');
      console.error('Calendar dashboard load error:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load calendar events
  const loadCalendarEvents = async () => {
    try {
      const startDate = moment(selectedDate).startOf('month').format('YYYY-MM-DD');
      const endDate = moment(selectedDate).endOf('month').format('YYYY-MM-DD');

      const params = {
        startDate,
        endDate,
        ...filters
      };

      const response = await request.get('/api/calendar/events', { params });
      if (response.success) {
        setCalendarEvents(response.result.events);
      }
    } catch (error) {
      message.error('Błąd podczas ładowania wydarzeń kalendarza');
      console.error('Calendar events load error:', error);
    }
  };

  // Load configuration data
  const loadConfigData = async () => {
    try {
      const [categoriesResponse, districtsResponse] = await Promise.all([
        request.get('/api/calendar/config/hvac-categories'),
        request.get('/api/calendar/config/warsaw-districts')
      ]);

      if (categoriesResponse.success) {
        setHvacCategories(categoriesResponse.result);
      }
      if (districtsResponse.success) {
        setWarsawDistricts(districtsResponse.result);
      }
    } catch (error) {
      console.error('Config data load error:', error);
    }
  };

  // Optimize Warsaw routes
  const handleOptimizeRoutes = async () => {
    try {
      const response = await request.get('/api/calendar/optimize/warsaw-routes', {
        params: { date: selectedDate }
      });
      if (response.success) {
        setOptimizedRoutes(response.result);
        setOptimizationModalVisible(true);
      }
    } catch (error) {
      message.error('Błąd podczas optymalizacji tras');
      console.error('Route optimization error:', error);
    }
  };

  useEffect(() => {
    loadDashboardData();
    loadConfigData();
  }, []);

  useEffect(() => {
    loadCalendarEvents();
  }, [selectedDate, filters]);

  // Render overview statistics
  const renderOverviewStats = () => {
    if (!dashboardData?.metrics) return null;

    return (
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Wydarzenia dzisiaj"
              value={dashboardData.metrics.eventsToday}
              prefix={<CalendarOutlined style={{ color: '#1890ff' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Wydarzenia w tym tygodniu"
              value={dashboardData.metrics.eventsThisWeek}
              prefix={<ClockCircleOutlined style={{ color: '#52c41a' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Nadchodzące wydarzenia"
              value={dashboardData.metrics.upcomingEvents}
              prefix={<WarningOutlined style={{ color: '#faad14' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Łączne wydarzenia"
              value={dashboardData.metrics.totalEvents}
              prefix={<CheckCircleOutlined style={{ color: '#722ed1' }} />}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  // Render category breakdown chart
  const renderCategoryChart = () => {
    if (!dashboardData?.categoryBreakdown) return null;

    const data = dashboardData.categoryBreakdown.map(cat => ({
      category: `${cat.icon} ${cat.name}`,
      count: cat.count,
      color: cat.color
    }));

    const config = {
      data,
      xField: 'category',
      yField: 'count',
      color: data.map(item => item.color),
      columnStyle: {
        radius: [4, 4, 0, 0]
      },
      label: {
        position: 'top'
      }
    };

    return (
      <Card title="Rozkład kategorii HVAC" extra={
        <Tooltip title="Liczba wydarzeń według kategorii HVAC">
          <WarningOutlined />
        </Tooltip>
      }>
        <Column {...config} height={300} />
      </Card>
    );
  };

  // Render district distribution chart
  const renderDistrictChart = () => {
    if (!dashboardData?.districtBreakdown) return null;

    const data = dashboardData.districtBreakdown.map(district => ({
      district: district.district,
      count: district.count,
      zone: district.zone
    }));

    const config = {
      data,
      angleField: 'count',
      colorField: 'district',
      radius: 0.8,
      label: {
        type: 'outer',
        content: '{name} ({percentage})'
      },
      interactions: [{ type: 'element-active' }]
    };

    return (
      <Card title="Rozkład dzielnic Warszawy" extra={
        <Tooltip title="Wydarzenia według dzielnic Warszawy">
          <EnvironmentOutlined />
        </Tooltip>
      }>
        <Pie {...config} height={300} />
      </Card>
    );
  };

  // Render calendar view
  const renderCalendarView = () => {
    const events = calendarEvents.map(event => ({
      id: event.id,
      title: event.title,
      start: event.start,
      end: event.end,
      backgroundColor: event.backgroundColor,
      borderColor: event.borderColor,
      textColor: event.textColor,
      extendedProps: {
        category: event.category,
        client: event.client,
        technician: event.technician,
        location: event.location,
        status: event.status,
        priority: event.priority
      }
    }));

    return (
      <Card
        title="Kalendarz HVAC"
        extra={
          <Space>
            <Select
              value={filters.category}
              onChange={(value) => setFilters({ ...filters, category: value })}
              style={{ width: 120 }}
            >
              <Option value="all">Wszystkie</Option>
              {hvacCategories.map(cat => (
                <Option key={cat.key} value={cat.key}>
                  {cat.icon} {cat.name}
                </Option>
              ))}
            </Select>
            <Select
              value={filters.district}
              onChange={(value) => setFilters({ ...filters, district: value })}
              style={{ width: 150 }}
            >
              <Option value="all">Wszystkie dzielnice</Option>
              {warsawDistricts.map(district => (
                <Option key={district.name} value={district.name}>
                  {district.name}
                </Option>
              ))}
            </Select>
            <Button
              type="primary"
              icon={<OptimizationOutlined />}
              onClick={handleOptimizeRoutes}
            >
              Optymalizuj trasy
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setCreateEventModalVisible(true)}
            >
              Nowe wydarzenie
            </Button>
          </Space>
        }
      >
        <FullCalendar
          plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
          headerToolbar={{
            left: 'prev,next today',
            center: 'title',
            right: 'dayGridMonth,timeGridWeek,timeGridDay'
          }}
          initialView="dayGridMonth"
          events={events}
          height="auto"
          locale="pl"
          firstDay={1}
          eventClick={(info) => {
            const event = info.event;
            Modal.info({
              title: event.title,
              content: (
                <div>
                  <p><strong>Kategoria:</strong> {event.extendedProps.category}</p>
                  <p><strong>Klient:</strong> {event.extendedProps.client?.name}</p>
                  <p><strong>Technik:</strong> {event.extendedProps.technician?.name || 'Nieprzypisany'}</p>
                  <p><strong>Lokalizacja:</strong> {event.extendedProps.location?.address}</p>
                  <p><strong>Status:</strong> {event.extendedProps.status}</p>
                  <p><strong>Priorytet:</strong> {event.extendedProps.priority}</p>
                </div>
              ),
              width: 600
            });
          }}
          dateClick={(info) => {
            setSelectedDate(info.dateStr);
          }}
        />
      </Card>
    );
  };

  // Render optimization modal
  const renderOptimizationModal = () => {
    if (!optimizedRoutes) return null;

    return (
      <Modal
        title={`Optymalizacja tras - ${optimizedRoutes.date}`}
        visible={optimizationModalVisible}
        onCancel={() => setOptimizationModalVisible(false)}
        footer={null}
        width={800}
      >
        <Alert
          message={`Zoptymalizowano ${optimizedRoutes.totalEvents} wydarzeń w ${optimizedRoutes.zones.length} strefach`}
          type="success"
          style={{ marginBottom: 16 }}
        />

        {Object.entries(optimizedRoutes.routesByZone).map(([zone, events]) => (
          <Card key={zone} size="small" style={{ marginBottom: 16 }}>
            <Title level={5}>Strefa: {zone.toUpperCase()}</Title>
            <Timeline>
              {events.map((event, index) => (
                <Timeline.Item
                  key={index}
                  color={event.backgroundColor}
                  dot={<Avatar size="small" style={{ backgroundColor: event.backgroundColor }}>
                    {event.categoryName?.charAt(0)}
                  </Avatar>}
                >
                  <div>
                    <div style={{ fontWeight: 'bold' }}>{event.title}</div>
                    <div style={{ color: '#666', fontSize: '12px' }}>
                      {formatDateTime(event.start)} - {event.location?.district}
                    </div>
                    <Tag color={event.priority === 'high' ? 'red' : 'default'} size="small">
                      {event.priority}
                    </Tag>
                  </div>
                </Timeline.Item>
              ))}
            </Timeline>
          </Card>
        ))}
      </Modal>
    );
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>
          <Text>Ładowanie kalendarza HVAC...</Text>
        </div>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>
          <CalendarOutlined /> Kalendarz HVAC
        </Title>
        <Text type="secondary">
          Zaawansowany system kalendarza z 3 kategoriami HVAC i optymalizacją tras dla Warszawy
        </Text>
      </div>

      {/* Overview Statistics */}
      {renderOverviewStats()}

      {/* Charts */}
      <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
        <Col xs={24} lg={12}>
          {renderCategoryChart()}
        </Col>
        <Col xs={24} lg={12}>
          {renderDistrictChart()}
        </Col>
      </Row>

      {/* Calendar View */}
      <div style={{ marginTop: 24 }}>
        {renderCalendarView()}
      </div>

      {/* Optimization Modal */}
      {renderOptimizationModal()}

      {/* Create Event Modal - placeholder */}
      <Modal
        title="Nowe wydarzenie kalendarza"
        visible={createEventModalVisible}
        onCancel={() => setCreateEventModalVisible(false)}
        footer={null}
      >
        <Text>Formularz tworzenia nowego wydarzenia będzie tutaj...</Text>
      </Modal>
    </div>
  );
};

export default CalendarDashboard;
