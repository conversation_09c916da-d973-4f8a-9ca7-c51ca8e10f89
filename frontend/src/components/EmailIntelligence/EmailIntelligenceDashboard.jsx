import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Timeline,
  Badge,
  Button,
  Statistic,
  Progress,
  List,
  Avatar,
  Tag,
  Spin,
  Alert,
  Typography,
  Tabs,
  Table,
  Space,
  Tooltip,
  Modal,
  Select,
  Input,
  Checkbox,
  Divider,
  notification
} from 'antd';
import {
  BrainCircuit,
  Mail,
  Calendar,
  DollarSign,
  TrendingUp,
  Users,
  Clock,
  CheckCircle,
  AlertTriangle,
  Zap,
  Trash2,
  Forward,
  Archive,
  Play,
  Filter,
  Search,
  BarChart3,
  Settings,
  Inbox,
  Send,
  Shield,
  Target
} from 'lucide-react';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { Search: SearchInput } = Input;

const EmailIntelligenceDashboard = () => {
  const [insights, setInsights] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [systemStatus, setSystemStatus] = useState({});

  // Inbox Zero specific state
  const [activeTab, setActiveTab] = useState('dashboard');
  const [emails, setEmails] = useState([]);
  const [selectedEmails, setSelectedEmails] = useState([]);
  const [emailFilter, setEmailFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [inboxZeroStats, setInboxZeroStats] = useState({});
  const [bulkActionModal, setBulkActionModal] = useState(false);
  const [triageModal, setTriageModal] = useState(false);
  const [selectedEmail, setSelectedEmail] = useState(null);

  useEffect(() => {
    fetchEmailInsights();
    fetchSystemStatus();
    fetchEmails();
    fetchInboxZeroStats();

    // Refresh every 30 seconds
    const interval = setInterval(() => {
      fetchEmailInsights();
      fetchSystemStatus();
      if (activeTab === 'inbox-zero') {
        fetchEmails();
        fetchInboxZeroStats();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [activeTab]);

  const fetchEmailInsights = async () => {
    try {
      const response = await fetch('http://localhost:8001/insights');
      if (!response.ok) throw new Error('Failed to fetch insights');
      const data = await response.json();
      setInsights(data);
      setError(null);
    } catch (error) {
      console.error('Error fetching insights:', error);
      setError('Failed to connect to Email Intelligence service');
    } finally {
      setLoading(false);
    }
  };

  const fetchSystemStatus = async () => {
    try {
      const response = await fetch('http://localhost:8001/health');
      if (!response.ok) throw new Error('Failed to fetch status');
      const data = await response.json();
      setSystemStatus(data);
    } catch (error) {
      console.error('Error fetching system status:', error);
    }
  };

  const triggerEmailProcessing = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:8001/process-emails', {
        method: 'POST'
      });
      if (!response.ok) throw new Error('Failed to trigger processing');
      
      // Refresh insights after processing
      setTimeout(() => {
        fetchEmailInsights();
        setLoading(false);
      }, 2000);
    } catch (error) {
      console.error('Error triggering email processing:', error);
      setError('Failed to trigger email processing');
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'healthy': return 'success';
      case 'unhealthy': return 'error';
      default: return 'warning';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'healthy': return <CheckCircle size={16} />;
      case 'unhealthy': return <AlertTriangle size={16} />;
      default: return <Clock size={16} />;
    }
  };

  // Inbox Zero Methods
  const fetchEmails = async () => {
    try {
      const response = await fetch('/api/email-intelligence/emails');
      if (!response.ok) throw new Error('Failed to fetch emails');
      const data = await response.json();
      setEmails(data.result?.emails || []);
    } catch (error) {
      console.error('Error fetching emails:', error);
    }
  };

  const fetchInboxZeroStats = async () => {
    try {
      const response = await fetch('/api/email-intelligence/inbox-zero-stats');
      if (!response.ok) throw new Error('Failed to fetch stats');
      const data = await response.json();
      setInboxZeroStats(data.result || {});
    } catch (error) {
      console.error('Error fetching inbox zero stats:', error);
    }
  };

  // 4 D's Framework Implementation
  const handleDelete = async (emailIds) => {
    try {
      await fetch('/api/email-intelligence/emails/delete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ emailIds })
      });
      notification.success({ message: 'Emails deleted successfully' });
      fetchEmails();
      fetchInboxZeroStats();
    } catch (error) {
      notification.error({ message: 'Failed to delete emails' });
    }
  };

  const handleDelegate = async (emailIds, assignee) => {
    try {
      await fetch('/api/email-intelligence/emails/delegate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ emailIds, assignee })
      });
      notification.success({ message: 'Emails delegated successfully' });
      fetchEmails();
    } catch (error) {
      notification.error({ message: 'Failed to delegate emails' });
    }
  };

  const handleDefer = async (emailIds, deferUntil) => {
    try {
      await fetch('/api/email-intelligence/emails/defer', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ emailIds, deferUntil })
      });
      notification.success({ message: 'Emails deferred successfully' });
      fetchEmails();
    } catch (error) {
      notification.error({ message: 'Failed to defer emails' });
    }
  };

  const handleDo = async (emailId, action) => {
    try {
      await fetch('/api/email-intelligence/emails/do', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ emailId, action })
      });
      notification.success({ message: 'Action completed successfully' });
      fetchEmails();
    } catch (error) {
      notification.error({ message: 'Failed to complete action' });
    }
  };

  const handleBulkUnsubscribe = async (emailIds) => {
    try {
      await fetch('/api/email-intelligence/emails/bulk-unsubscribe', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ emailIds })
      });
      notification.success({ message: 'Bulk unsubscribe completed' });
      fetchEmails();
      fetchInboxZeroStats();
    } catch (error) {
      notification.error({ message: 'Failed to unsubscribe' });
    }
  };

  const handleBlockSender = async (emailIds) => {
    try {
      await fetch('/api/email-intelligence/emails/block-sender', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ emailIds })
      });
      notification.success({ message: 'Senders blocked successfully' });
      fetchEmails();
    } catch (error) {
      notification.error({ message: 'Failed to block senders' });
    }
  };

  if (error) {
    return (
      <Alert
        message="Email Intelligence Service Unavailable"
        description={error}
        type="warning"
        showIcon
        action={
          <Button size="small" onClick={fetchEmailInsights}>
            Retry
          </Button>
        }
      />
    );
  }

  return (
    <>
      <div className="email-intelligence-dashboard">
        <Row gutter={[16, 16]}>
          {/* Header */}
          <Col span={24}>
            <Card>
              <Row justify="space-between" align="middle">
                <Col>
                  <Title level={3} style={{ margin: 0 }}>
                    🧠 Enhanced Email Intelligence + Inbox Zero
                  </Title>
                  <Text type="secondary">
                    AI-powered email management with Inbox Zero methodology
                  </Text>
                </Col>
                <Col>
                  <Space>
                    <Button
                      type="primary"
                      icon={<Zap size={16} />}
                      onClick={triggerEmailProcessing}
                      loading={loading}
                    >
                      Process Emails
                    </Button>
                    <Button
                      icon={<Target size={16} />}
                      onClick={() => setActiveTab('inbox-zero')}
                    >
                      Inbox Zero
                    </Button>
                  </Space>
                </Col>
              </Row>
            </Card>
          </Col>

          {/* Main Content Tabs */}
          <Col span={24}>
            <Card>
              <Tabs
                activeKey={activeTab}
                onChange={setActiveTab}
                items={[
                  {
                    key: 'dashboard',
                    label: (
                      <span>
                        <BarChart3 size={16} style={{ marginRight: 8 }} />
                        Intelligence Dashboard
                      </span>
                    ),
                    children: renderIntelligenceDashboard()
                  },
                  {
                    key: 'inbox-zero',
                    label: (
                      <span>
                        <Inbox size={16} style={{ marginRight: 8 }} />
                        Inbox Zero Management
                      </span>
                    ),
                    children: renderInboxZeroInterface()
                  },
                  {
                    key: 'analytics',
                    label: (
                      <span>
                        <TrendingUp size={16} style={{ marginRight: 8 }} />
                        Email Analytics
                      </span>
                    ),
                    children: renderEmailAnalytics()
                  }
                ]}
              />
            </Card>
          </Col>
        </Row>

        <style jsx>{`
          .email-intelligence-dashboard .metric-card {
            text-align: center;
            border: 1px solid #f0f0f0;
          }

          .timeline-item {
            margin-bottom: 8px;
          }

          .action-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
          }

          .action-description {
            margin-bottom: 4px;
          }

          .suggestion-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
          }

          .suggestion-actions {
            display: flex;
            flex-direction: column;
            gap: 4px;
          }

          .inbox-zero-card {
            transition: all 0.3s ease;
            cursor: pointer;
          }

          .inbox-zero-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
          }
        `}</style>
      </div>

      {/* Modals */}
      {modalsAndStyles}
    </>
  );

  // Render Intelligence Dashboard (existing functionality)
  function renderIntelligenceDashboard() {
    return (
      <Row gutter={[16, 16]}>
        {/* System Status */}
        <Col span={24}>
          <Card title="🔧 System Status" size="small">
            <Row gutter={16}>
              <Col span={6}>
                <Badge 
                  status={getStatusColor(systemStatus.email_processor)} 
                  text="Email Processor" 
                />
              </Col>
              <Col span={6}>
                <Badge 
                  status={getStatusColor(systemStatus.ai_analyzer)} 
                  text="AI Analyzer" 
                />
              </Col>
              <Col span={6}>
                <Badge 
                  status={getStatusColor(systemStatus.weaviate_client)} 
                  text="Weaviate" 
                />
              </Col>
              <Col span={6}>
                <Badge 
                  status={getStatusColor(systemStatus.crm_integrator)} 
                  text="CRM Integration" 
                />
              </Col>
            </Row>
          </Card>
        </Col>

        {/* Key Metrics */}
        <Col span={24}>
          <Card title="📊 Intelligence Metrics" loading={loading}>
            <Row gutter={16}>
              <Col span={6}>
                <Card size="small" className="metric-card">
                  <Statistic
                    title="Emails Processed"
                    value={insights.emails_processed || 0}
                    prefix={<Mail size={20} />}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card size="small" className="metric-card">
                  <Statistic
                    title="Insights Generated"
                    value={insights.insights_generated || 0}
                    prefix={<BrainCircuit size={20} />}
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card size="small" className="metric-card">
                  <Statistic
                    title="Events Created"
                    value={insights.events_created || 0}
                    prefix={<Calendar size={20} />}
                    valueStyle={{ color: '#fa8c16' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card size="small" className="metric-card">
                  <Statistic
                    title="Offers Generated"
                    value={insights.offers_generated || 0}
                    prefix={<DollarSign size={20} />}
                    valueStyle={{ color: '#eb2f96' }}
                  />
                </Card>
              </Col>
            </Row>
          </Card>
        </Col>

        {/* Recent AI Actions */}
        <Col span={12}>
          <Card title="🤖 Recent AI Actions" loading={loading}>
            <Timeline>
              {insights.recent_actions?.map((action, index) => (
                <Timeline.Item 
                  key={index}
                  dot={getStatusIcon(action.status)}
                  color={getStatusColor(action.status)}
                >
                  <div className="timeline-item">
                    <div className="action-header">
                      <Text strong>{action.type}</Text>
                      <Tag color={getStatusColor(action.status)}>
                        {action.status}
                      </Tag>
                    </div>
                    <div className="action-description">
                      <Text type="secondary">{action.description}</Text>
                    </div>
                    <div className="action-time">
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {new Date(action.timestamp).toLocaleString()}
                      </Text>
                    </div>
                  </div>
                </Timeline.Item>
              )) || (
                <Timeline.Item>
                  <Text type="secondary">No recent actions</Text>
                </Timeline.Item>
              )}
            </Timeline>
          </Card>
        </Col>

        {/* Suggested Actions */}
        <Col span={12}>
          <Card title="💡 AI Suggestions" loading={loading}>
            <List
              dataSource={insights.suggested_actions || []}
              renderItem={(suggestion, index) => (
                <List.Item key={index}>
                  <List.Item.Meta
                    avatar={
                      <Avatar 
                        style={{ 
                          backgroundColor: suggestion.priority_color || '#1890ff' 
                        }}
                        icon={<BrainCircuit size={16} />}
                      />
                    }
                    title={
                      <div className="suggestion-header">
                        <Text strong>{suggestion.title}</Text>
                        <Tag color={suggestion.priority_color}>
                          {suggestion.priority || 'medium'}
                        </Tag>
                      </div>
                    }
                    description={suggestion.description}
                  />
                  <div className="suggestion-actions">
                    <Button 
                      type="primary" 
                      size="small"
                      onClick={() => handleSuggestionAction(suggestion)}
                    >
                      Execute
                    </Button>
                    <Button 
                      size="small" 
                      style={{ marginLeft: 8 }}
                    >
                      Dismiss
                    </Button>
                  </div>
                </List.Item>
              )}
              locale={{ emptyText: 'No suggestions available' }}
            />
          </Card>
        </Col>

        {/* Processing Progress */}
        <Col span={24}>
          <Card title="⚡ Processing Status" size="small">
            <Row gutter={16} align="middle">
              <Col span={8}>
                <Text>Email Processing:</Text>
                <Progress 
                  percent={85} 
                  size="small" 
                  status="active"
                  format={() => 'Active'}
                />
              </Col>
              <Col span={8}>
                <Text>AI Analysis:</Text>
                <Progress 
                  percent={92} 
                  size="small" 
                  status="active"
                  format={() => 'Running'}
                />
              </Col>
              <Col span={8}>
                <Text>CRM Integration:</Text>
                <Progress 
                  percent={78} 
                  size="small" 
                  status="active"
                  format={() => 'Syncing'}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    );
  }

  // Render Inbox Zero Interface
  function renderInboxZeroInterface() {
    const filteredEmails = emails.filter(email => {
      const matchesFilter = emailFilter === 'all' || email.category === emailFilter;
      const matchesSearch = !searchTerm ||
        email.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
        email.sender.toLowerCase().includes(searchTerm.toLowerCase());
      return matchesFilter && matchesSearch;
    });

    const emailColumns = [
      {
        title: '',
        dataIndex: 'id',
        width: 50,
        render: (id) => (
          <Checkbox
            checked={selectedEmails.includes(id)}
            onChange={(e) => {
              if (e.target.checked) {
                setSelectedEmails([...selectedEmails, id]);
              } else {
                setSelectedEmails(selectedEmails.filter(emailId => emailId !== id));
              }
            }}
          />
        )
      },
      {
        title: 'From',
        dataIndex: 'sender',
        width: 200,
        render: (sender, record) => (
          <div>
            <Text strong>{sender}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.senderEmail}
            </Text>
          </div>
        )
      },
      {
        title: 'Subject',
        dataIndex: 'subject',
        render: (subject, record) => (
          <div>
            <Text>{subject}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {new Date(record.timestamp).toLocaleString()}
            </Text>
          </div>
        )
      },
      {
        title: 'Category',
        dataIndex: 'category',
        width: 120,
        render: (category) => {
          const categoryColors = {
            'customer': 'blue',
            'newsletter': 'orange',
            'cold': 'red',
            'internal': 'green',
            'urgent': 'volcano'
          };
          return <Tag color={categoryColors[category] || 'default'}>{category}</Tag>;
        }
      },
      {
        title: 'Actions',
        width: 200,
        render: (_, record) => (
          <Space size="small">
            <Tooltip title="Delete">
              <Button
                size="small"
                icon={<Trash2 size={14} />}
                onClick={() => handleDelete([record.id])}
              />
            </Tooltip>
            <Tooltip title="Delegate">
              <Button
                size="small"
                icon={<Forward size={14} />}
                onClick={() => {
                  setSelectedEmail(record);
                  setTriageModal(true);
                }}
              />
            </Tooltip>
            <Tooltip title="Archive">
              <Button
                size="small"
                icon={<Archive size={14} />}
                onClick={() => handleDefer([record.id], 'archive')}
              />
            </Tooltip>
            <Tooltip title="Quick Action">
              <Button
                size="small"
                icon={<Play size={14} />}
                onClick={() => handleDo(record.id, 'quick_reply')}
              />
            </Tooltip>
          </Space>
        )
      }
    ];

    return (
      <Row gutter={[16, 16]}>
        {/* Inbox Zero Stats */}
        <Col span={24}>
          <Row gutter={16}>
            <Col span={6}>
              <Card size="small">
                <Statistic
                  title="Inbox Count"
                  value={inboxZeroStats.inbox_count || 0}
                  prefix={<Inbox size={20} />}
                  valueStyle={{ color: inboxZeroStats.inbox_count > 0 ? '#fa8c16' : '#52c41a' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card size="small">
                <Statistic
                  title="Processed Today"
                  value={inboxZeroStats.processed_today || 0}
                  prefix={<CheckCircle size={20} />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card size="small">
                <Statistic
                  title="Unsubscribed"
                  value={inboxZeroStats.unsubscribed_count || 0}
                  prefix={<Shield size={20} />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card size="small">
                <Statistic
                  title="Blocked Senders"
                  value={inboxZeroStats.blocked_count || 0}
                  prefix={<AlertTriangle size={20} />}
                  valueStyle={{ color: '#f5222d' }}
                />
              </Card>
            </Col>
          </Row>
        </Col>

        {/* Email Management Controls */}
        <Col span={24}>
          <Card title="📧 Email Management" size="small">
            <Row gutter={16} align="middle">
              <Col span={8}>
                <SearchInput
                  placeholder="Search emails..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  style={{ width: '100%' }}
                />
              </Col>
              <Col span={4}>
                <Select
                  value={emailFilter}
                  onChange={setEmailFilter}
                  style={{ width: '100%' }}
                >
                  <Option value="all">All Emails</Option>
                  <Option value="customer">Customer</Option>
                  <Option value="newsletter">Newsletter</Option>
                  <Option value="cold">Cold Email</Option>
                  <Option value="internal">Internal</Option>
                  <Option value="urgent">Urgent</Option>
                </Select>
              </Col>
              <Col span={12}>
                <Space>
                  <Button
                    disabled={selectedEmails.length === 0}
                    onClick={() => setBulkActionModal(true)}
                  >
                    Bulk Actions ({selectedEmails.length})
                  </Button>
                  <Button
                    type="primary"
                    icon={<Target size={16} />}
                    onClick={() => {
                      // Apply Inbox Zero methodology to all emails
                      notification.info({ message: 'Applying Inbox Zero methodology...' });
                    }}
                  >
                    Apply Inbox Zero
                  </Button>
                </Space>
              </Col>
            </Row>
          </Card>
        </Col>

        {/* Email Table */}
        <Col span={24}>
          <Card title="📬 Email Inbox" loading={loading}>
            <Table
              columns={emailColumns}
              dataSource={filteredEmails}
              rowKey="id"
              pagination={{
                pageSize: 20,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `Total ${total} emails`
              }}
              scroll={{ x: 800 }}
            />
          </Card>
        </Col>
      </Row>
    );
  }

  // Render Email Analytics
  function renderEmailAnalytics() {
    return (
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card title="📊 Email Analytics Dashboard">
            <Row gutter={16}>
              <Col span={8}>
                <Card size="small">
                  <Statistic
                    title="Emails per Day"
                    value={inboxZeroStats.daily_average || 0}
                    suffix="avg"
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Card>
              </Col>
              <Col span={8}>
                <Card size="small">
                  <Statistic
                    title="Response Time"
                    value={inboxZeroStats.avg_response_time || 0}
                    suffix="min"
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Card>
              </Col>
              <Col span={8}>
                <Card size="small">
                  <Statistic
                    title="Inbox Zero Days"
                    value={inboxZeroStats.zero_days || 0}
                    suffix="this month"
                    valueStyle={{ color: '#fa8c16' }}
                  />
                </Card>
              </Col>
            </Row>
            <Divider />
            <Text type="secondary">
              Detailed analytics coming soon with email patterns, productivity insights, and AI recommendations.
            </Text>
          </Card>
        </Col>
      </Row>
    );
  }

  // Add modals and styles to the main return statement
  const modalsAndStyles = (
    <>
      {/* Bulk Action Modal */}
      <Modal
        title="Bulk Email Actions"
        open={bulkActionModal}
        onCancel={() => setBulkActionModal(false)}
        footer={null}
        width={600}
      >
        <div style={{ padding: '20px 0' }}>
          <Title level={4}>Apply Inbox Zero Methodology</Title>
          <Text type="secondary">
            Choose an action for {selectedEmails.length} selected emails:
          </Text>

          <div style={{ marginTop: 20 }}>
            <Row gutter={16}>
              <Col span={12}>
                <Card
                  hoverable
                  onClick={() => {
                    handleDelete(selectedEmails);
                    setBulkActionModal(false);
                    setSelectedEmails([]);
                  }}
                  style={{ textAlign: 'center', marginBottom: 16 }}
                >
                  <Trash2 size={24} style={{ color: '#f5222d', marginBottom: 8 }} />
                  <div><strong>Delete</strong></div>
                  <Text type="secondary">Remove unnecessary emails</Text>
                </Card>
              </Col>
              <Col span={12}>
                <Card
                  hoverable
                  onClick={() => {
                    handleBulkUnsubscribe(selectedEmails);
                    setBulkActionModal(false);
                    setSelectedEmails([]);
                  }}
                  style={{ textAlign: 'center', marginBottom: 16 }}
                >
                  <Shield size={24} style={{ color: '#1890ff', marginBottom: 8 }} />
                  <div><strong>Unsubscribe</strong></div>
                  <Text type="secondary">Bulk unsubscribe from newsletters</Text>
                </Card>
              </Col>
              <Col span={12}>
                <Card
                  hoverable
                  onClick={() => {
                    handleBlockSender(selectedEmails);
                    setBulkActionModal(false);
                    setSelectedEmails([]);
                  }}
                  style={{ textAlign: 'center' }}
                >
                  <AlertTriangle size={24} style={{ color: '#fa8c16', marginBottom: 8 }} />
                  <div><strong>Block Senders</strong></div>
                  <Text type="secondary">Block cold email senders</Text>
                </Card>
              </Col>
              <Col span={12}>
                <Card
                  hoverable
                  onClick={() => {
                    handleDefer(selectedEmails, 'archive');
                    setBulkActionModal(false);
                    setSelectedEmails([]);
                  }}
                  style={{ textAlign: 'center' }}
                >
                  <Archive size={24} style={{ color: '#52c41a', marginBottom: 8 }} />
                  <div><strong>Archive</strong></div>
                  <Text type="secondary">Archive for later review</Text>
                </Card>
              </Col>
            </Row>
          </div>
        </div>
      </Modal>

      {/* Email Triage Modal */}
      <Modal
        title="Email Triage - 4 D's Framework"
        open={triageModal}
        onCancel={() => setTriageModal(false)}
        footer={null}
        width={700}
      >
        {selectedEmail && (
          <div style={{ padding: '20px 0' }}>
            <div style={{ marginBottom: 20, padding: 16, backgroundColor: '#f5f5f5', borderRadius: 8 }}>
              <Text strong>From: </Text>{selectedEmail.sender}<br />
              <Text strong>Subject: </Text>{selectedEmail.subject}<br />
              <Text strong>Category: </Text>
              <Tag color="blue">{selectedEmail.category}</Tag>
            </div>

            <Title level={4}>Choose Action (4 D's Framework):</Title>

            <Row gutter={16}>
              <Col span={12}>
                <Card
                  hoverable
                  onClick={() => {
                    handleDelete([selectedEmail.id]);
                    setTriageModal(false);
                    setSelectedEmail(null);
                  }}
                  style={{ textAlign: 'center', marginBottom: 16 }}
                >
                  <Trash2 size={24} style={{ color: '#f5222d', marginBottom: 8 }} />
                  <div><strong>Delete</strong></div>
                  <Text type="secondary">Remove if not needed</Text>
                </Card>
              </Col>
              <Col span={12}>
                <Card
                  hoverable
                  onClick={() => {
                    handleDelegate([selectedEmail.id], 'team');
                    setTriageModal(false);
                    setSelectedEmail(null);
                  }}
                  style={{ textAlign: 'center', marginBottom: 16 }}
                >
                  <Forward size={24} style={{ color: '#1890ff', marginBottom: 8 }} />
                  <div><strong>Delegate</strong></div>
                  <Text type="secondary">Forward to right person</Text>
                </Card>
              </Col>
              <Col span={12}>
                <Card
                  hoverable
                  onClick={() => {
                    handleDefer([selectedEmail.id], 'later');
                    setTriageModal(false);
                    setSelectedEmail(null);
                  }}
                  style={{ textAlign: 'center' }}
                >
                  <Clock size={24} style={{ color: '#fa8c16', marginBottom: 8 }} />
                  <div><strong>Defer</strong></div>
                  <Text type="secondary">Schedule for later</Text>
                </Card>
              </Col>
              <Col span={12}>
                <Card
                  hoverable
                  onClick={() => {
                    handleDo(selectedEmail.id, 'immediate');
                    setTriageModal(false);
                    setSelectedEmail(null);
                  }}
                  style={{ textAlign: 'center' }}
                >
                  <Play size={24} style={{ color: '#52c41a', marginBottom: 8 }} />
                  <div><strong>Do</strong></div>
                  <Text type="secondary">Handle immediately (&lt;2min)</Text>
                </Card>
              </Col>
            </Row>
          </div>
        )}
      </Modal>
    </>
  );

  function handleSuggestionAction(suggestion) {
    // Implement suggestion action handling
    console.log('Executing suggestion:', suggestion);
    // This could trigger API calls to execute the suggested action
  }
};

export default EmailIntelligenceDashboard;
