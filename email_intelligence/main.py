"""
Email Intelligence System - Main Application
FastAPI-based service for processing emails and transcriptions with AI analysis
"""
import asyncio
import logging
from contextlib import asynccontextmanager
from typing import Dict, List

from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

from config import config, get_email_accounts_config
from services.email_processor import EmailProcessor
from services.ai_analyzer import AIAnalyzer
from services.weaviate_client import WeaviateHVACClient
from services.crm_integrator import CRMIntegrator
from services.pdf_proposal_generator import PDFProposalGenerator
from services.calendar_intelligence import CalendarIntelligence
from utils.logger import setup_logger


# Setup logging
logger = setup_logger(__name__)


# Pydantic models for API
class EmailAnalysisRequest(BaseModel):
    email_content: str
    sender: str
    subject: str
    attachments: List[str] = []


class EmailAnalysisResponse(BaseModel):
    analysis_id: str
    classification: Dict
    customer_insights: Dict
    calendar_events: List[Dict]
    offer_proposals: List[Dict]
    crm_actions: List[Dict]


class SystemStatus(BaseModel):
    status: str
    email_processor: str
    ai_analyzer: str
    weaviate_client: str
    crm_integrator: str
    processed_emails_today: int


# Global service instances
email_processor = None
ai_analyzer = None
weaviate_client = None
crm_integrator = None
pdf_generator = None
calendar_intelligence = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management"""
    global email_processor, ai_analyzer, weaviate_client, crm_integrator, pdf_generator, calendar_intelligence

    logger.info("🚀 Starting FULMARK Email Intelligence System...")

    try:
        # Initialize core services
        email_processor = EmailProcessor(get_email_accounts_config())
        ai_analyzer = AIAnalyzer()
        weaviate_client = WeaviateHVACClient()
        crm_integrator = CRMIntegrator()

        # Initialize new enhanced services
        pdf_generator = PDFProposalGenerator()
        calendar_intelligence = CalendarIntelligence()

        # Setup Weaviate schema
        await weaviate_client.setup_schema()

        # Start background email processing
        asyncio.create_task(background_email_processing())

        logger.info("✅ FULMARK Email Intelligence System started successfully!")
        logger.info("🎯 Enhanced features: Auto Kanban, PDF Proposals, Calendar Intelligence")

        yield

    except Exception as e:
        logger.error(f"❌ Failed to start FULMARK Email Intelligence System: {e}")
        raise
    finally:
        logger.info("🛑 Shutting down FULMARK Email Intelligence System...")


# FastAPI app
app = FastAPI(
    title="FULMARK HVAC Email Intelligence",
    description="AI-powered email and transcription analysis for HVAC CRM",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/", response_model=Dict)
async def root():
    """Root endpoint"""
    return {
        "message": "FULMARK HVAC Email Intelligence System",
        "version": "1.0.0",
        "status": "operational"
    }


@app.get("/health", response_model=SystemStatus)
async def health_check():
    """Health check endpoint"""
    try:
        # Check service statuses
        email_status = "healthy" if email_processor else "unhealthy"
        ai_status = "healthy" if ai_analyzer else "unhealthy"
        weaviate_status = "healthy" if weaviate_client else "unhealthy"
        crm_status = "healthy" if crm_integrator else "unhealthy"
        
        # Get processed emails count (mock for now)
        processed_today = 0  # TODO: Implement actual counter
        
        return SystemStatus(
            status="healthy" if all([
                email_processor, ai_analyzer, weaviate_client, crm_integrator
            ]) else "unhealthy",
            email_processor=email_status,
            ai_analyzer=ai_status,
            weaviate_client=weaviate_status,
            crm_integrator=crm_status,
            processed_emails_today=processed_today
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail="Health check failed")


@app.post("/analyze", response_model=EmailAnalysisResponse)
async def analyze_email(request: EmailAnalysisRequest):
    """Analyze email content with AI"""
    try:
        logger.info(f"📧 Analyzing email from: {request.sender}")
        
        # Prepare data for analysis
        email_data = {
            "content": request.email_content,
            "sender": request.sender,
            "subject": request.subject,
            "attachments": request.attachments
        }
        
        # Run AI analysis
        analysis_result = await ai_analyzer.analyze_communication(email_data)
        
        # Store in Weaviate
        await weaviate_client.store_communication(email_data, analysis_result)
        
        # Integrate with CRM
        crm_actions = await crm_integrator.process_analysis(analysis_result)
        
        return EmailAnalysisResponse(
            analysis_id=analysis_result.get("id", ""),
            classification=analysis_result.get("classification", {}),
            customer_insights=analysis_result.get("customer_insights", {}),
            calendar_events=analysis_result.get("calendar_events", []),
            offer_proposals=analysis_result.get("offer_proposals", []),
            crm_actions=crm_actions
        )
        
    except Exception as e:
        logger.error(f"Email analysis failed: {e}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")


@app.post("/process-emails")
async def trigger_email_processing(background_tasks: BackgroundTasks):
    """Manually trigger email processing"""
    try:
        background_tasks.add_task(process_emails_batch)
        return {"message": "Email processing triggered", "status": "started"}
    except Exception as e:
        logger.error(f"Failed to trigger email processing: {e}")
        raise HTTPException(status_code=500, detail="Failed to trigger processing")


@app.get("/insights")
async def get_insights():
    """Get AI-generated insights dashboard data"""
    try:
        # Get recent insights from Weaviate
        insights = await weaviate_client.get_recent_insights()
        
        return {
            "emails_processed": insights.get("emails_processed", 0),
            "insights_generated": insights.get("insights_generated", 0),
            "events_created": insights.get("events_created", 0),
            "offers_generated": insights.get("offers_generated", 0),
            "recent_actions": insights.get("recent_actions", []),
            "suggested_actions": insights.get("suggested_actions", [])
        }
    except Exception as e:
        logger.error(f"Failed to get insights: {e}")
        raise HTTPException(status_code=500, detail="Failed to get insights")


@app.get("/search")
async def semantic_search(query: str, limit: int = 10):
    """Perform semantic search on customer data"""
    try:
        results = await weaviate_client.semantic_search(query, limit)
        return {"query": query, "results": results}
    except Exception as e:
        logger.error(f"Semantic search failed: {e}")
        raise HTTPException(status_code=500, detail="Search failed")


@app.post("/generate-proposal")
async def generate_pdf_proposal(
    offer_data: Dict,
    customer_data: Dict,
    analysis_data: Dict = {}
):
    """Generate professional PDF proposal from AI analysis"""
    try:
        logger.info(f"📄 Generating PDF proposal for {customer_data.get('email', 'unknown')}")

        # Generate PDF proposal
        pdf_path = await pdf_generator.generate_proposal(
            offer_data, customer_data, analysis_data
        )

        if pdf_path:
            return {
                "status": "success",
                "pdf_path": pdf_path,
                "message": "PDF proposal generated successfully",
                "fulmark_branding": True
            }
        else:
            raise HTTPException(status_code=500, detail="PDF generation failed")

    except Exception as e:
        logger.error(f"PDF proposal generation failed: {e}")
        raise HTTPException(status_code=500, detail=f"PDF generation failed: {str(e)}")


@app.post("/structure-calendar")
async def structure_calendar_events(unstructured_events: List[Dict]):
    """Structure poorly organized calendar events using AI"""
    try:
        logger.info(f"🗓️ Structuring {len(unstructured_events)} calendar events")

        # Structure events using AI
        structured_events = await calendar_intelligence.structure_calendar_events(
            unstructured_events
        )

        return {
            "status": "success",
            "original_count": len(unstructured_events),
            "structured_count": len(structured_events),
            "structured_events": structured_events,
            "optimization_applied": True,
            "categories": {
                "Serwis": "czerwony - maintenance, repairs",
                "Nowa Instalacja": "niebieski - installations, upgrades",
                "Oględziny": "zielony - inspections, consultations"
            }
        }

    except Exception as e:
        logger.error(f"Calendar structuring failed: {e}")
        raise HTTPException(status_code=500, detail=f"Calendar structuring failed: {str(e)}")


@app.post("/auto-kanban")
async def create_auto_kanban(
    email_content: str,
    sender: str,
    subject: str = "",
    force_create: bool = False
):
    """Automatically create Kanban cards from email/transcription analysis"""
    try:
        logger.info(f"🎯 Auto-creating Kanban card from email: {sender}")

        # Prepare email data
        email_data = {
            "content": email_content,
            "sender": sender,
            "subject": subject
        }

        # Run AI analysis with enhanced Kanban creation
        analysis_result = await ai_analyzer.analyze_communication(email_data)

        # Force Kanban creation if requested
        if force_create:
            analysis_result['classification']['auto_create_kanban'] = True

        # Integrate with CRM to create Kanban cards
        crm_actions = await crm_integrator.process_analysis(analysis_result)

        # Filter for Kanban-related actions
        kanban_actions = [
            action for action in crm_actions
            if 'service_order' in action.get('action', '') or 'opportunity' in action.get('action', '')
        ]

        return {
            "status": "success",
            "kanban_cards_created": len(kanban_actions),
            "kanban_actions": kanban_actions,
            "ai_analysis": {
                "classification": analysis_result.get('classification', {}),
                "customer_insights": analysis_result.get('customer_insights', {}),
                "confidence": analysis_result.get('classification', {}).get('confidence_score', 0.5)
            },
            "fulmark_intelligence": "Powered by FULMARK AI Email Intelligence"
        }

    except Exception as e:
        logger.error(f"Auto Kanban creation failed: {e}")
        raise HTTPException(status_code=500, detail=f"Auto Kanban creation failed: {str(e)}")


async def background_email_processing():
    """Background task for continuous email processing"""
    logger.info("🔄 Starting background email processing...")
    
    while True:
        try:
            await process_emails_batch()
            # Wait 5 minutes before next batch
            await asyncio.sleep(300)
        except Exception as e:
            logger.error(f"Background email processing error: {e}")
            # Wait 1 minute before retry
            await asyncio.sleep(60)


async def process_emails_batch():
    """Process a batch of emails"""
    try:
        logger.info("📬 Processing email batch...")
        
        # Fetch emails from all accounts
        all_emails = await email_processor.fetch_all_emails()
        
        for email_data in all_emails:
            try:
                # Analyze with AI
                analysis = await ai_analyzer.analyze_communication(email_data)
                
                # Store in Weaviate
                await weaviate_client.store_communication(email_data, analysis)
                
                # Integrate with CRM
                await crm_integrator.process_analysis(analysis)
                
                logger.info(f"✅ Processed email: {email_data.get('subject', 'No subject')}")
                
            except Exception as e:
                logger.error(f"Failed to process email: {e}")
                continue
        
        logger.info(f"📊 Batch complete: {len(all_emails)} emails processed")
        
    except Exception as e:
        logger.error(f"Email batch processing failed: {e}")


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level=config.LOG_LEVEL.lower()
    )
