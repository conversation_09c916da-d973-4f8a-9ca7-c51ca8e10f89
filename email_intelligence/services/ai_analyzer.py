"""
AI Analysis Service
CrewAI and LangChain-based intelligent analysis of emails and transcriptions
"""
import asyncio
import json
import uuid
from typing import Dict, List, Optional
from datetime import datetime

from crewai import Agent, Task, Crew
from langchain_openai import ChatOpenAI
from langchain.tools import Tool
import openai

from config import config, AI_PROMPTS, HVAC_CATEGORIES
from utils.logger import setup_logger

logger = setup_logger(__name__)


class AIAnalyzer:
    """AI-powered analysis service for HVAC communications"""
    
    def __init__(self):
        self.llm = None
        self.agents = {}
        self._setup_ai_services()
    
    def _setup_ai_services(self):
        """Setup AI services and agents"""
        try:
            # Initialize OpenAI LLM
            self.llm = ChatOpenAI(
                model=config.OPENAI_MODEL,
                api_key=config.OPENAI_API_KEY,
                temperature=0.1  # Low temperature for consistent analysis
            )
            
            # Setup CrewAI agents
            self._setup_agents()
            
            logger.info("✅ AI services initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to setup AI services: {e}")
            raise
    
    def _setup_agents(self):
        """Setup specialized HVAC AI agents"""
        
        # Email Classification Agent
        self.agents['classifier'] = Agent(
            role='HVAC Email Classification Specialist',
            goal='Classify and categorize HVAC-related emails and transcriptions with high accuracy',
            backstory=AI_PROMPTS['email_classifier'],
            llm=self.llm,
            verbose=True,
            allow_delegation=False
        )
        
        # Customer Intelligence Agent
        self.agents['customer_analyst'] = Agent(
            role='HVAC Customer Intelligence Analyst',
            goal='Extract deep customer insights and business opportunities from communications',
            backstory=AI_PROMPTS['customer_analyst'],
            llm=self.llm,
            verbose=True,
            allow_delegation=False
        )
        
        # Calendar Structuring Agent
        self.agents['calendar_organizer'] = Agent(
            role='HVAC Calendar and Scheduling Optimizer',
            goal='Structure and optimize calendar events from unstructured communications',
            backstory=AI_PROMPTS['calendar_organizer'],
            llm=self.llm,
            verbose=True,
            allow_delegation=False
        )
        
        # Offer Generation Agent
        self.agents['offer_generator'] = Agent(
            role='HVAC Offer and Proposal Specialist',
            goal='Generate professional HVAC offers and proposals from customer communications',
            backstory=AI_PROMPTS['offer_generator'],
            llm=self.llm,
            verbose=True,
            allow_delegation=False
        )
    
    async def analyze_communication(self, communication_data: Dict) -> Dict:
        """Run complete AI analysis on communication data"""
        try:
            analysis_id = str(uuid.uuid4())
            logger.info(f"🧠 Starting AI analysis: {analysis_id}")
            
            # Prepare communication content
            content = self._prepare_content(communication_data)
            
            # Run parallel analysis tasks
            tasks = [
                self._classify_communication(content),
                self._analyze_customer_insights(content),
                self._structure_calendar_events(content),
                self._generate_offers(content)
            ]
            
            # Execute all tasks
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Compile results
            analysis_result = {
                'id': analysis_id,
                'timestamp': datetime.now().isoformat(),
                'communication_data': communication_data,
                'classification': results[0] if not isinstance(results[0], Exception) else {},
                'customer_insights': results[1] if not isinstance(results[1], Exception) else {},
                'calendar_events': results[2] if not isinstance(results[2], Exception) else [],
                'offer_proposals': results[3] if not isinstance(results[3], Exception) else [],
                'processing_status': 'completed'
            }
            
            # Log any errors
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Task {i} failed: {result}")
            
            logger.info(f"✅ AI analysis completed: {analysis_id}")
            return analysis_result
            
        except Exception as e:
            logger.error(f"❌ AI analysis failed: {e}")
            return {
                'id': str(uuid.uuid4()),
                'timestamp': datetime.now().isoformat(),
                'error': str(e),
                'processing_status': 'failed'
            }
    
    def _prepare_content(self, communication_data: Dict) -> str:
        """Prepare communication content for analysis"""
        content_parts = []
        
        # Add email content
        if communication_data.get('body'):
            content_parts.append(f"Email Body:\n{communication_data['body']}")
        
        if communication_data.get('subject'):
            content_parts.append(f"Subject: {communication_data['subject']}")
        
        # Add transcription content
        if communication_data.get('transcriptions'):
            for transcription in communication_data['transcriptions']:
                content_parts.append(f"Transcription ({transcription['filename']}):\n{transcription['text']}")
        
        # Add sender information
        if communication_data.get('sender'):
            content_parts.append(f"Sender: {communication_data['sender']}")
        
        return "\n\n".join(content_parts)
    
    async def _classify_communication(self, content: str) -> Dict:
        """Classify communication using AI agent"""
        try:
            task = Task(
                description=f"""
                Analyze this HVAC communication and provide detailed classification for Polish HVAC market:

                Content:
                {content}

                Provide classification in JSON format:
                {{
                    "type": "one of: {HVAC_CATEGORIES['service_types']}",
                    "priority": "one of: {HVAC_CATEGORIES['priority_levels']}",
                    "customer_type": "new|existing|potential",
                    "equipment_mentioned": ["list of equipment types with brands"],
                    "urgency_indicators": ["list of urgency signals in Polish"],
                    "sentiment": "positive|neutral|negative",
                    "confidence_score": 0.0-1.0,
                    "auto_create_kanban": true,
                    "suggested_kanban_stage": "BACKLOG|SCHEDULED|IN_PROGRESS",
                    "estimated_revenue": "estimated value in PLN",
                    "seasonal_factor": "high|medium|low based on Warsaw climate",
                    "polish_hvac_keywords": ["detected Polish HVAC terms"],
                    "building_type_detected": "kamienica|blok|dom_jednorodzinny|biurowiec|sklep",
                    "equipment_brands_mentioned": ["Daikin|LG|Mitsubishi|Carrier|Toshiba"],
                    "service_complexity": "simple|medium|complex|very_complex",
                    "requires_site_visit": true|false,
                    "potential_upsell": ["additional services that could be offered"]
                }}

                Focus on Polish HVAC market specifics:
                - Recognize Polish HVAC terminology (klimatyzacja, wentylacja, pompa ciepła)
                - Consider Warsaw building types and climate
                - Identify seasonal patterns (heating season, cooling season)
                - Detect equipment brands popular in Poland
                - Assess complexity for Polish technician skills
                """,
                agent=self.agents['classifier'],
                expected_output="JSON enhanced classification object"
            )

            crew = Crew(
                agents=[self.agents['classifier']],
                tasks=[task],
                verbose=False
            )

            result = crew.kickoff()
            return self._parse_json_result(result)

        except Exception as e:
            logger.error(f"Classification failed: {e}")
            return {}
    
    async def _analyze_customer_insights(self, content: str) -> Dict:
        """Analyze customer insights using AI agent"""
        try:
            task = Task(
                description=f"""
                Extract deep customer insights from this HVAC communication:
                
                Content:
                {content}
                
                Provide insights in JSON format:
                {{
                    "customer_needs": ["list of identified needs"],
                    "pain_points": ["list of customer problems"],
                    "budget_indicators": "low|medium|high|premium",
                    "decision_timeline": "immediate|weeks|months|planning",
                    "equipment_preferences": ["preferred brands/types"],
                    "upselling_opportunities": ["potential additional services"],
                    "satisfaction_level": "very_low|low|neutral|high|very_high",
                    "churn_risk": "low|medium|high",
                    "lifetime_value_potential": "low|medium|high|premium"
                }}
                """,
                agent=self.agents['customer_analyst'],
                expected_output="JSON customer insights object"
            )
            
            crew = Crew(
                agents=[self.agents['customer_analyst']],
                tasks=[task],
                verbose=False
            )
            
            result = crew.kickoff()
            return self._parse_json_result(result)
            
        except Exception as e:
            logger.error(f"Customer analysis failed: {e}")
            return {}
    
    async def _structure_calendar_events(self, content: str) -> List[Dict]:
        """Structure calendar events using AI agent"""
        try:
            task = Task(
                description=f"""
                Extract and structure calendar events from this HVAC communication:
                
                Content:
                {content}
                
                Identify potential calendar events and provide in JSON format:
                [
                    {{
                        "title": "Event title",
                        "category": "one of: {HVAC_CATEGORIES['calendar_categories']}",
                        "suggested_date": "YYYY-MM-DD or 'not_specified'",
                        "suggested_time": "HH:MM or 'not_specified'",
                        "duration_hours": 1-8,
                        "location": "customer address or 'to_be_determined'",
                        "required_skills": ["list of technician skills needed"],
                        "equipment_needed": ["list of equipment/tools"],
                        "priority": "low|medium|high|urgent",
                        "description": "detailed event description"
                    }}
                ]
                """,
                agent=self.agents['calendar_organizer'],
                expected_output="JSON array of calendar events"
            )
            
            crew = Crew(
                agents=[self.agents['calendar_organizer']],
                tasks=[task],
                verbose=False
            )
            
            result = crew.kickoff()
            parsed_result = self._parse_json_result(result)
            return parsed_result if isinstance(parsed_result, list) else []
            
        except Exception as e:
            logger.error(f"Calendar structuring failed: {e}")
            return []
    
    async def _generate_offers(self, content: str) -> List[Dict]:
        """Generate offer proposals using AI agent"""
        try:
            task = Task(
                description=f"""
                Generate professional HVAC offer proposals from this communication:

                Content:
                {content}

                If this communication indicates a sales opportunity, provide offers in JSON format:
                [
                    {{
                        "offer_type": "installation|maintenance|repair|consultation",
                        "equipment_recommended": ["specific equipment models with brands (Daikin, LG, Mitsubishi)"],
                        "service_description": "detailed service description in Polish",
                        "estimated_price_range": "low-high in PLN (realistic Polish market prices)",
                        "installation_timeline": "estimated timeline in Polish",
                        "value_proposition": "key benefits for customer in Polish",
                        "next_steps": ["recommended actions in Polish"],
                        "urgency": "low|medium|high",
                        "confidence": 0.0-1.0,
                        "kanban_stage": "NEW_LEAD|QUALIFIED|PROPOSAL",
                        "auto_create_kanban": true,
                        "pdf_proposal_ready": true,
                        "fulmark_branding": true,
                        "seasonal_considerations": "consider Warsaw climate and seasonal demand",
                        "competition_analysis": "brief competitive positioning vs other Warsaw HVAC companies"
                    }}
                ]

                Focus on realistic Polish HVAC market opportunities. Consider:
                - Warsaw climate specifics
                - Polish building types (kamienice, bloki, domy jednorodzinne)
                - Seasonal HVAC demand patterns
                - Local competition and pricing
                - Energy efficiency regulations in Poland

                If no sales opportunity is detected, return empty array.
                """,
                agent=self.agents['offer_generator'],
                expected_output="JSON array of enhanced offer proposals or empty array"
            )

            crew = Crew(
                agents=[self.agents['offer_generator']],
                tasks=[task],
                verbose=False
            )

            result = crew.kickoff()
            parsed_result = self._parse_json_result(result)
            return parsed_result if isinstance(parsed_result, list) else []

        except Exception as e:
            logger.error(f"Offer generation failed: {e}")
            return []
    
    def _parse_json_result(self, result) -> Dict:
        """Parse JSON result from AI agent"""
        try:
            # Extract JSON from result string
            result_str = str(result)
            
            # Find JSON content
            start_idx = result_str.find('{')
            end_idx = result_str.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = result_str[start_idx:end_idx]
                return json.loads(json_str)
            
            # Try to find array
            start_idx = result_str.find('[')
            end_idx = result_str.rfind(']') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = result_str[start_idx:end_idx]
                return json.loads(json_str)
            
            logger.warning(f"Could not parse JSON from result: {result_str[:200]}...")
            return {}
            
        except Exception as e:
            logger.error(f"JSON parsing error: {e}")
            return {}
