"""
CRM Integration Service
Integrates AI analysis results with the existing HVAC CRM system
"""
import asyncio
import json
from typing import Dict, List
from datetime import datetime, timedelta
import httpx

from config import config
from utils.logger import setup_logger

logger = setup_logger(__name__)


class CRMIntegrator:
    """Service for integrating AI analysis with HVAC CRM"""
    
    def __init__(self):
        self.crm_api_url = config.CRM_API_URL
        self.api_key = config.CRM_API_KEY
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def process_analysis(self, analysis_result: Dict) -> List[Dict]:
        """Process AI analysis and create CRM entities"""
        crm_actions = []
        
        try:
            # Extract communication data
            communication_data = analysis_result.get('communication_data', {})
            sender = communication_data.get('sender', '')
            
            # Process customer updates
            customer_action = await self._update_or_create_customer(
                sender, 
                analysis_result.get('customer_insights', {}),
                analysis_result.get('classification', {})
            )
            if customer_action:
                crm_actions.append(customer_action)
            
            # Process service orders
            service_actions = await self._create_service_orders(
                analysis_result.get('classification', {}),
                analysis_result.get('calendar_events', []),
                sender
            )
            crm_actions.extend(service_actions)
            
            # Process opportunities
            opportunity_actions = await self._create_opportunities(
                analysis_result.get('offer_proposals', []),
                analysis_result.get('customer_insights', {}),
                sender
            )
            crm_actions.extend(opportunity_actions)
            
            # Process calendar events
            calendar_actions = await self._create_calendar_events(
                analysis_result.get('calendar_events', []),
                sender
            )
            crm_actions.extend(calendar_actions)
            
            logger.info(f"✅ CRM integration completed: {len(crm_actions)} actions")
            return crm_actions
            
        except Exception as e:
            logger.error(f"❌ CRM integration failed: {e}")
            return []
    
    async def _update_or_create_customer(self, email: str, customer_insights: Dict, classification: Dict) -> Dict:
        """Update or create customer in CRM"""
        try:
            if not email:
                return {}
            
            # Find existing customer
            existing_customer = await self._find_customer_by_email(email)
            
            # Prepare customer data
            customer_data = {
                "email": email,
                "name": self._extract_name_from_email(email),
                "healthScore": self._calculate_health_score(customer_insights),
                "churnProbability": self._map_churn_risk(customer_insights.get('churn_risk', 'medium')),
                "lifetimeValue": self._map_lifetime_value(customer_insights.get('lifetime_value_potential', 'medium')),
                "lastContactDate": datetime.now().isoformat(),
                "aiInsights": {
                    "lastAnalysis": datetime.now().isoformat(),
                    "customerNeeds": customer_insights.get('customer_needs', []),
                    "painPoints": customer_insights.get('pain_points', []),
                    "budgetIndicators": customer_insights.get('budget_indicators', 'medium'),
                    "equipmentPreferences": customer_insights.get('equipment_preferences', []),
                    "upselling_opportunities": customer_insights.get('upselling_opportunities', []),
                    "sentiment": classification.get('sentiment', 'neutral')
                }
            }
            
            if existing_customer:
                # Update existing customer
                response = await self.client.patch(
                    f"{self.crm_api_url}/client/{existing_customer['_id']}",
                    json=customer_data,
                    headers=self._get_headers()
                )
                
                if response.status_code == 200:
                    return {
                        "action": "customer_updated",
                        "customer_id": existing_customer['_id'],
                        "email": email,
                        "status": "success"
                    }
            else:
                # Create new customer
                customer_data.update({
                    "phone": "",
                    "address": "",
                    "buildingType": "residential",  # Default
                    "serviceArea": "Warsaw",  # Default
                    "contractType": "standard",
                    "priority": classification.get('priority', 'medium')
                })
                
                response = await self.client.post(
                    f"{self.crm_api_url}/client",
                    json=customer_data,
                    headers=self._get_headers()
                )
                
                if response.status_code == 200:
                    new_customer = response.json()
                    return {
                        "action": "customer_created",
                        "customer_id": new_customer.get('_id'),
                        "email": email,
                        "status": "success"
                    }
            
            return {"action": "customer_update", "status": "failed"}
            
        except Exception as e:
            logger.error(f"❌ Customer update failed: {e}")
            return {"action": "customer_update", "status": "error", "error": str(e)}
    
    async def _create_service_orders(self, classification: Dict, calendar_events: List[Dict], customer_email: str) -> List[Dict]:
        """Create enhanced service orders based on AI analysis"""
        service_actions = []

        try:
            # Check if this is a service request or if auto_create_kanban is enabled
            should_create_order = (
                classification.get('type') in ['maintenance', 'repair', 'installation', 'consultation'] or
                classification.get('auto_create_kanban', False)
            )

            if should_create_order:
                # Find customer
                customer = await self._find_customer_by_email(customer_email)
                if not customer:
                    return service_actions

                # Determine optimal stage based on AI analysis
                suggested_stage = classification.get('suggested_kanban_stage', 'BACKLOG')
                if classification.get('requires_site_visit', False):
                    suggested_stage = 'SCHEDULED'

                # Create enhanced service order
                service_order_data = {
                    "client": customer['_id'],
                    "orderNumber": self._generate_order_number(),
                    "stage": suggested_stage,
                    "priority": classification.get('priority', 'medium'),
                    "serviceType": classification.get('type', 'maintenance'),
                    "description": self._generate_smart_description(classification),
                    "scheduledDate": self._extract_date_from_events(calendar_events),
                    "estimatedDuration": self._estimate_duration_enhanced(classification),
                    "estimatedRevenue": self._parse_revenue(classification.get('estimated_revenue', '0')),
                    "equipmentMentioned": classification.get('equipment_mentioned', []),
                    "brandsMentioned": classification.get('equipment_brands_mentioned', []),
                    "buildingType": classification.get('building_type_detected', 'unknown'),
                    "serviceComplexity": classification.get('service_complexity', 'medium'),
                    "requiresSiteVisit": classification.get('requires_site_visit', False),
                    "seasonalFactor": classification.get('seasonal_factor', 'medium'),
                    "aiInsights": {
                        "createdFromEmail": True,
                        "analysisDate": datetime.now().isoformat(),
                        "classification": classification,
                        "urgencyIndicators": classification.get('urgency_indicators', []),
                        "polishHvacKeywords": classification.get('polish_hvac_keywords', []),
                        "potentialUpsell": classification.get('potential_upsell', []),
                        "confidenceScore": classification.get('confidence_score', 0.5),
                        "autoGenerated": True,
                        "fulmarkIntelligence": "Generated by FULMARK AI Email Intelligence System"
                    }
                }

                response = await self.client.post(
                    f"{self.crm_api_url}/serviceorder",
                    json=service_order_data,
                    headers=self._get_headers()
                )

                if response.status_code == 200:
                    service_order = response.json()
                    service_actions.append({
                        "action": "enhanced_service_order_created",
                        "service_order_id": service_order.get('_id'),
                        "order_number": service_order_data['orderNumber'],
                        "customer_email": customer_email,
                        "stage": suggested_stage,
                        "estimated_revenue": service_order_data['estimatedRevenue'],
                        "ai_confidence": classification.get('confidence_score', 0.5),
                        "status": "success"
                    })

                    logger.info(f"🎯 Enhanced Kanban card created: {service_order_data['orderNumber']} for {customer_email}")

        except Exception as e:
            logger.error(f"❌ Enhanced service order creation failed: {e}")
            service_actions.append({
                "action": "enhanced_service_order_creation",
                "status": "error",
                "error": str(e)
            })

        return service_actions
    
    async def _create_opportunities(self, offer_proposals: List[Dict], customer_insights: Dict, customer_email: str) -> List[Dict]:
        """Create sales opportunities based on offer proposals"""
        opportunity_actions = []
        
        try:
            if not offer_proposals:
                return opportunity_actions
            
            # Find customer
            customer = await self._find_customer_by_email(customer_email)
            if not customer:
                return opportunity_actions
            
            for offer in offer_proposals:
                opportunity_data = {
                    "client": customer['_id'],
                    "title": f"AI-Generated Opportunity: {offer.get('offer_type', 'HVAC Service')}",
                    "stage": "NEW_LEAD",
                    "value": self._estimate_opportunity_value(offer),
                    "probability": self._calculate_probability(offer.get('confidence', 0.5)),
                    "expectedCloseDate": self._calculate_close_date(offer.get('urgency', 'medium')),
                    "description": offer.get('service_description', ''),
                    "equipmentType": self._extract_equipment_type(offer.get('equipment_recommended', [])),
                    "projectComplexity": self._assess_complexity(offer),
                    "aiInsights": {
                        "createdFromEmail": True,
                        "analysisDate": datetime.now().isoformat(),
                        "offerProposal": offer,
                        "customerInsights": customer_insights,
                        "leadScore": self._calculate_lead_score(customer_insights, offer)
                    }
                }
                
                response = await self.client.post(
                    f"{self.crm_api_url}/opportunity",
                    json=opportunity_data,
                    headers=self._get_headers()
                )
                
                if response.status_code == 200:
                    opportunity = response.json()
                    opportunity_actions.append({
                        "action": "opportunity_created",
                        "opportunity_id": opportunity.get('_id'),
                        "title": opportunity_data['title'],
                        "value": opportunity_data['value'],
                        "customer_email": customer_email,
                        "status": "success"
                    })
        
        except Exception as e:
            logger.error(f"❌ Opportunity creation failed: {e}")
            opportunity_actions.append({
                "action": "opportunity_creation",
                "status": "error",
                "error": str(e)
            })
        
        return opportunity_actions
    
    async def _create_calendar_events(self, calendar_events: List[Dict], customer_email: str) -> List[Dict]:
        """Create calendar events in CRM"""
        calendar_actions = []
        
        try:
            # Find customer
            customer = await self._find_customer_by_email(customer_email)
            if not customer:
                return calendar_actions
            
            for event in calendar_events:
                # Map to CRM calendar format
                event_data = {
                    "title": event.get('title', 'AI-Generated Event'),
                    "description": event.get('description', ''),
                    "category": event.get('category', 'Serwis'),
                    "startDate": self._parse_event_date(event.get('suggested_date')),
                    "startTime": event.get('suggested_time', '09:00'),
                    "duration": event.get('duration_hours', 2),
                    "client": customer['_id'],
                    "priority": event.get('priority', 'medium'),
                    "requiredSkills": event.get('required_skills', []),
                    "equipmentNeeded": event.get('equipment_needed', []),
                    "aiGenerated": True,
                    "createdFromEmail": True
                }
                
                # Note: This would integrate with your calendar system
                # For now, we'll just log the action
                calendar_actions.append({
                    "action": "calendar_event_suggested",
                    "event_data": event_data,
                    "customer_email": customer_email,
                    "status": "pending_review"
                })
        
        except Exception as e:
            logger.error(f"❌ Calendar event creation failed: {e}")
            calendar_actions.append({
                "action": "calendar_event_creation",
                "status": "error",
                "error": str(e)
            })
        
        return calendar_actions
    
    async def _find_customer_by_email(self, email: str) -> Dict:
        """Find customer by email in CRM"""
        try:
            response = await self.client.get(
                f"{self.crm_api_url}/client/search",
                params={"email": email},
                headers=self._get_headers()
            )
            
            if response.status_code == 200:
                customers = response.json()
                return customers[0] if customers else None
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Customer search failed: {e}")
            return None
    
    def _get_headers(self) -> Dict:
        """Get API headers"""
        headers = {"Content-Type": "application/json"}
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"
        return headers
    
    def _extract_name_from_email(self, email: str) -> str:
        """Extract name from email address"""
        try:
            if '<' in email and '>' in email:
                name_part = email.split('<')[0].strip()
                if name_part:
                    return name_part.strip('"')
            return email.split('@')[0].replace('.', ' ').title()
        except Exception:
            return email
    
    def _calculate_health_score(self, customer_insights: Dict) -> int:
        """Calculate customer health score"""
        base_score = 75
        
        # Adjust based on satisfaction
        satisfaction = customer_insights.get('satisfaction_level', 'neutral')
        if satisfaction in ['high', 'very_high']:
            base_score += 15
        elif satisfaction in ['low', 'very_low']:
            base_score -= 20
        
        # Adjust based on churn risk
        churn_risk = customer_insights.get('churn_risk', 'medium')
        if churn_risk == 'low':
            base_score += 10
        elif churn_risk == 'high':
            base_score -= 15
        
        return max(0, min(100, base_score))
    
    def _map_churn_risk(self, churn_risk: str) -> float:
        """Map churn risk to probability"""
        mapping = {
            'low': 0.1,
            'medium': 0.3,
            'high': 0.7
        }
        return mapping.get(churn_risk, 0.3)
    
    def _map_lifetime_value(self, ltv: str) -> int:
        """Map lifetime value to numeric value"""
        mapping = {
            'low': 5000,
            'medium': 15000,
            'high': 30000,
            'premium': 50000
        }
        return mapping.get(ltv, 15000)
    
    def _generate_order_number(self) -> str:
        """Generate service order number"""
        now = datetime.now()
        return f"SO-{now.year}-{now.month:02d}{now.day:02d}-{now.hour:02d}{now.minute:02d}"
    
    def _extract_date_from_events(self, calendar_events: List[Dict]) -> str:
        """Extract earliest date from calendar events"""
        for event in calendar_events:
            date = event.get('suggested_date')
            if date and date != 'not_specified':
                return date
        
        # Default to tomorrow
        tomorrow = datetime.now() + timedelta(days=1)
        return tomorrow.strftime('%Y-%m-%d')
    
    def _estimate_duration(self, service_type: str) -> int:
        """Estimate service duration in hours"""
        duration_map = {
            'installation': 6,
            'maintenance': 2,
            'repair': 3,
            'consultation': 1,
            'inspection': 1
        }
        return duration_map.get(service_type, 2)
    
    def _estimate_opportunity_value(self, offer: Dict) -> int:
        """Estimate opportunity value"""
        offer_type = offer.get('offer_type', 'maintenance')
        
        value_map = {
            'installation': 15000,
            'maintenance': 2000,
            'repair': 3000,
            'consultation': 500
        }
        
        base_value = value_map.get(offer_type, 2000)
        
        # Adjust based on confidence
        confidence = offer.get('confidence', 0.5)
        return int(base_value * (0.5 + confidence))
    
    def _calculate_probability(self, confidence: float) -> int:
        """Calculate opportunity probability percentage"""
        return max(10, min(90, int(confidence * 100)))
    
    def _calculate_close_date(self, urgency: str) -> str:
        """Calculate expected close date"""
        now = datetime.now()
        
        if urgency == 'high':
            close_date = now + timedelta(days=7)
        elif urgency == 'medium':
            close_date = now + timedelta(days=30)
        else:
            close_date = now + timedelta(days=60)
        
        return close_date.strftime('%Y-%m-%d')
    
    def _extract_equipment_type(self, equipment_list: List[str]) -> str:
        """Extract primary equipment type"""
        if not equipment_list:
            return 'air_conditioner'
        
        # Simple mapping
        equipment_str = ' '.join(equipment_list).lower()
        if 'heat pump' in equipment_str or 'pompa' in equipment_str:
            return 'heat_pump'
        elif 'ventilation' in equipment_str or 'wentylacja' in equipment_str:
            return 'ventilation'
        else:
            return 'air_conditioner'
    
    def _assess_complexity(self, offer: Dict) -> str:
        """Assess project complexity"""
        offer_type = offer.get('offer_type', 'maintenance')
        
        if offer_type == 'installation':
            return 'high'
        elif offer_type in ['repair', 'maintenance']:
            return 'medium'
        else:
            return 'low'
    
    def _calculate_lead_score(self, customer_insights: Dict, offer: Dict) -> int:
        """Calculate lead score"""
        base_score = 50
        
        # Budget indicators
        budget = customer_insights.get('budget_indicators', 'medium')
        if budget in ['high', 'premium']:
            base_score += 20
        elif budget == 'low':
            base_score -= 15
        
        # Decision timeline
        timeline = customer_insights.get('decision_timeline', 'months')
        if timeline == 'immediate':
            base_score += 25
        elif timeline == 'weeks':
            base_score += 10
        
        # Offer confidence
        confidence = offer.get('confidence', 0.5)
        base_score += int(confidence * 20)
        
        return max(0, min(100, base_score))
    
    def _parse_event_date(self, date_str: str) -> str:
        """Parse event date"""
        if not date_str or date_str == 'not_specified':
            tomorrow = datetime.now() + timedelta(days=1)
            return tomorrow.strftime('%Y-%m-%d')
        return date_str

    def _generate_smart_description(self, classification: Dict) -> str:
        """Generate intelligent service description based on AI analysis"""
        service_type = classification.get('type', 'service')
        equipment = classification.get('equipment_mentioned', [])
        brands = classification.get('equipment_brands_mentioned', [])
        keywords = classification.get('polish_hvac_keywords', [])
        building_type = classification.get('building_type_detected', 'unknown')

        description_parts = []

        # Service type in Polish
        service_map = {
            'installation': 'Instalacja',
            'maintenance': 'Konserwacja',
            'repair': 'Naprawa',
            'consultation': 'Konsultacja',
            'inspection': 'Oględziny'
        }

        description_parts.append(f"{service_map.get(service_type, 'Serwis')} HVAC")

        # Add equipment details
        if equipment:
            description_parts.append(f"- Sprzęt: {', '.join(equipment)}")

        # Add brands
        if brands:
            description_parts.append(f"- Marki: {', '.join(brands)}")

        # Add building type
        building_map = {
            'kamienica': 'Kamienica',
            'blok': 'Blok mieszkalny',
            'dom_jednorodzinny': 'Dom jednorodzinny',
            'biurowiec': 'Biurowiec',
            'sklep': 'Lokal handlowy'
        }

        if building_type != 'unknown':
            description_parts.append(f"- Typ budynku: {building_map.get(building_type, building_type)}")

        # Add AI keywords
        if keywords:
            description_parts.append(f"- Słowa kluczowe: {', '.join(keywords[:3])}")  # Limit to 3

        description_parts.append("🤖 Automatycznie utworzone przez FULMARK AI")

        return "\n".join(description_parts)

    def _estimate_duration_enhanced(self, classification: Dict) -> int:
        """Enhanced duration estimation based on AI analysis"""
        service_type = classification.get('type', 'maintenance')
        complexity = classification.get('service_complexity', 'medium')
        requires_visit = classification.get('requires_site_visit', False)

        # Base duration
        base_duration = self._estimate_duration(service_type)

        # Adjust for complexity
        complexity_multiplier = {
            'simple': 0.7,
            'medium': 1.0,
            'complex': 1.5,
            'very_complex': 2.0
        }

        duration = int(base_duration * complexity_multiplier.get(complexity, 1.0))

        # Add time for site visit
        if requires_visit:
            duration += 1

        return max(1, min(8, duration))  # Between 1-8 hours

    def _parse_revenue(self, revenue_str: str) -> int:
        """Parse revenue string to numeric value"""
        try:
            if not revenue_str or revenue_str == '0':
                return 0

            # Extract numbers from string like "1000-2000 PLN"
            import re
            numbers = re.findall(r'\d+', str(revenue_str))

            if numbers:
                # Take average if range, otherwise first number
                if len(numbers) >= 2:
                    return int((int(numbers[0]) + int(numbers[1])) / 2)
                else:
                    return int(numbers[0])

            return 0

        except Exception:
            return 0
