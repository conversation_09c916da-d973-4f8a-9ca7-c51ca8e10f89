"""
PDF Proposal Generator Service
Automatically generates professional HVAC proposals with Fulmark branding
"""
import asyncio
import os
from datetime import datetime, timedelta
from typing import Dict, List
from pathlib import Path

from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import cm
from reportlab.lib.colors import HexColor
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT

from utils.logger import setup_logger

logger = setup_logger(__name__)


class PDFProposalGenerator:
    """Service for generating professional HVAC proposals"""
    
    def __init__(self):
        self.output_dir = Path("generated_proposals")
        self.output_dir.mkdir(exist_ok=True)
        
        # Fulmark branding colors
        self.primary_color = HexColor('#1890ff')  # Ant Design blue
        self.secondary_color = HexColor('#52c41a')  # Success green
        self.text_color = HexColor('#262626')
        
        self._setup_styles()
    
    def _setup_styles(self):
        """Setup document styles"""
        self.styles = getSampleStyleSheet()
        
        # Custom styles for Fulmark branding
        self.styles.add(ParagraphStyle(
            name='FulmarkTitle',
            parent=self.styles['Heading1'],
            fontSize=24,
            textColor=self.primary_color,
            alignment=TA_CENTER,
            spaceAfter=20
        ))
        
        self.styles.add(ParagraphStyle(
            name='FulmarkSubtitle',
            parent=self.styles['Heading2'],
            fontSize=16,
            textColor=self.secondary_color,
            spaceAfter=12
        ))
        
        self.styles.add(ParagraphStyle(
            name='FulmarkBody',
            parent=self.styles['Normal'],
            fontSize=11,
            textColor=self.text_color,
            spaceAfter=8
        ))
    
    async def generate_proposal(self, offer_data: Dict, customer_data: Dict, analysis_data: Dict) -> str:
        """Generate professional PDF proposal"""
        try:
            logger.info(f"🎯 Generating PDF proposal for {customer_data.get('email', 'unknown')}")
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            customer_name = customer_data.get('name', 'Customer').replace(' ', '_')
            filename = f"FULMARK_Proposal_{customer_name}_{timestamp}.pdf"
            filepath = self.output_dir / filename
            
            # Create PDF document
            doc = SimpleDocTemplate(
                str(filepath),
                pagesize=A4,
                rightMargin=2*cm,
                leftMargin=2*cm,
                topMargin=2*cm,
                bottomMargin=2*cm
            )
            
            # Build content
            content = []
            
            # Header
            content.extend(self._build_header())
            
            # Customer information
            content.extend(self._build_customer_section(customer_data))
            
            # Offer details
            content.extend(self._build_offer_section(offer_data, analysis_data))
            
            # Equipment specifications
            content.extend(self._build_equipment_section(offer_data))
            
            # Pricing
            content.extend(self._build_pricing_section(offer_data))
            
            # Next steps
            content.extend(self._build_next_steps_section(offer_data))
            
            # Footer
            content.extend(self._build_footer())
            
            # Build PDF
            doc.build(content)
            
            logger.info(f"✅ PDF proposal generated: {filename}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"❌ PDF proposal generation failed: {e}")
            return ""
    
    def _build_header(self) -> List:
        """Build document header with Fulmark branding"""
        content = []
        
        # Company logo placeholder (you can add actual logo)
        content.append(Paragraph(
            "<b>FULMARK</b> - Klimatyzacja i Wentylacja",
            self.styles['FulmarkTitle']
        ))
        
        content.append(Paragraph(
            "Profesjonalne usługi HVAC w Warszawie",
            self.styles['FulmarkSubtitle']
        ))
        
        content.append(Spacer(1, 20))
        
        # Contact info
        contact_info = """
        <b>Kontakt:</b><br/>
        📧 Email: <EMAIL><br/>
        📞 Telefon: +48 XXX XXX XXX<br/>
        🌐 Website: www.fulmark.pl<br/>
        📍 Warszawa, Polska
        """
        
        content.append(Paragraph(contact_info, self.styles['FulmarkBody']))
        content.append(Spacer(1, 30))
        
        return content
    
    def _build_customer_section(self, customer_data: Dict) -> List:
        """Build customer information section"""
        content = []
        
        content.append(Paragraph("PROPOZYCJA OFERTY", self.styles['FulmarkTitle']))
        content.append(Spacer(1, 20))
        
        # Customer details
        customer_info = f"""
        <b>Dla:</b> {customer_data.get('name', 'Szanowny Kliencie')}<br/>
        <b>Email:</b> {customer_data.get('email', '')}<br/>
        <b>Data:</b> {datetime.now().strftime('%d.%m.%Y')}<br/>
        <b>Ważność oferty:</b> {(datetime.now() + timedelta(days=30)).strftime('%d.%m.%Y')}
        """
        
        content.append(Paragraph(customer_info, self.styles['FulmarkBody']))
        content.append(Spacer(1, 20))
        
        return content
    
    def _build_offer_section(self, offer_data: Dict, analysis_data: Dict) -> List:
        """Build main offer section"""
        content = []
        
        content.append(Paragraph("SZCZEGÓŁY OFERTY", self.styles['FulmarkSubtitle']))
        
        # Service description
        service_desc = offer_data.get('service_description', 'Profesjonalne usługi HVAC')
        content.append(Paragraph(f"<b>Usługa:</b> {service_desc}", self.styles['FulmarkBody']))
        
        # Value proposition
        value_prop = offer_data.get('value_proposition', 'Najwyższa jakość usług HVAC')
        content.append(Paragraph(f"<b>Korzyści:</b> {value_prop}", self.styles['FulmarkBody']))
        
        # Timeline
        timeline = offer_data.get('installation_timeline', 'Do uzgodnienia')
        content.append(Paragraph(f"<b>Czas realizacji:</b> {timeline}", self.styles['FulmarkBody']))
        
        # AI insights
        confidence = analysis_data.get('confidence', 0.5)
        content.append(Paragraph(
            f"<b>Dopasowanie do potrzeb:</b> {int(confidence * 100)}% (analiza AI)",
            self.styles['FulmarkBody']
        ))
        
        content.append(Spacer(1, 15))
        
        return content
    
    def _build_equipment_section(self, offer_data: Dict) -> List:
        """Build equipment specifications section"""
        content = []
        
        equipment_list = offer_data.get('equipment_recommended', [])
        if equipment_list:
            content.append(Paragraph("REKOMENDOWANY SPRZĘT", self.styles['FulmarkSubtitle']))
            
            for equipment in equipment_list:
                content.append(Paragraph(f"• {equipment}", self.styles['FulmarkBody']))
            
            content.append(Spacer(1, 15))
        
        return content
    
    def _build_pricing_section(self, offer_data: Dict) -> List:
        """Build pricing section"""
        content = []
        
        content.append(Paragraph("WYCENA", self.styles['FulmarkSubtitle']))
        
        price_range = offer_data.get('estimated_price_range', 'Do wyceny')
        content.append(Paragraph(f"<b>Szacunkowa cena:</b> {price_range}", self.styles['FulmarkBody']))
        
        # Pricing table
        pricing_data = [
            ['Pozycja', 'Opis', 'Cena'],
            ['Usługa podstawowa', offer_data.get('service_description', 'Usługa HVAC'), price_range],
            ['', '<i>Szczegółowa wycena po oględzinach</i>', '']
        ]
        
        pricing_table = Table(pricing_data, colWidths=[4*cm, 8*cm, 4*cm])
        pricing_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), self.primary_color),
            ('TEXTCOLOR', (0, 0), (-1, 0), HexColor('#ffffff')),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), HexColor('#f5f5f5')),
            ('GRID', (0, 0), (-1, -1), 1, HexColor('#d9d9d9'))
        ]))
        
        content.append(pricing_table)
        content.append(Spacer(1, 20))
        
        return content
    
    def _build_next_steps_section(self, offer_data: Dict) -> List:
        """Build next steps section"""
        content = []
        
        content.append(Paragraph("NASTĘPNE KROKI", self.styles['FulmarkSubtitle']))
        
        next_steps = offer_data.get('next_steps', [
            'Kontakt telefoniczny w celu ustalenia szczegółów',
            'Wizyta techniczna i dokładna wycena',
            'Realizacja usługi'
        ])
        
        for i, step in enumerate(next_steps, 1):
            content.append(Paragraph(f"{i}. {step}", self.styles['FulmarkBody']))
        
        content.append(Spacer(1, 20))
        
        return content
    
    def _build_footer(self) -> List:
        """Build document footer"""
        content = []
        
        footer_text = """
        <b>FULMARK - Twój partner w klimatyzacji</b><br/>
        ✅ Autoryzowany dealer Daikin, LG, Mitsubishi<br/>
        ✅ Doświadczenie w branży HVAC<br/>
        ✅ Gwarancja na wszystkie usługi<br/>
        ✅ Serwis 24/7<br/><br/>
        <i>Oferta wygenerowana automatycznie przez system FULMARK AI</i>
        """
        
        content.append(Paragraph(footer_text, self.styles['FulmarkBody']))
        
        return content
