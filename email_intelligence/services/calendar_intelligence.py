"""
Calendar Intelligence Service
Structures poorly organized calendar events using AI analysis
"""
import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import re

from crewai import Agent, Task, Crew
from langchain_openai import ChatOpenAI

from config import config, AI_PROMPTS
from utils.logger import setup_logger

logger = setup_logger(__name__)


class CalendarIntelligence:
    """AI-powered calendar structuring and optimization service"""
    
    def __init__(self):
        self.llm = ChatOpenAI(
            model=config.OPENAI_MODEL,
            api_key=config.OPENAI_API_KEY,
            temperature=0.1
        )
        self._setup_agents()
    
    def _setup_agents(self):
        """Setup specialized calendar AI agents"""
        
        # Calendar Structuring Agent
        self.calendar_agent = Agent(
            role='HVAC Calendar Optimization Specialist',
            goal='Structure and optimize calendar events for maximum efficiency',
            backstory="""
            You are an expert in HVAC service scheduling with deep knowledge of:
            - Warsaw traffic patterns and optimal routing
            - HVAC service time requirements
            - Seasonal demand patterns in Poland
            - Technician skill matching
            - Customer priority management
            """,
            llm=self.llm,
            verbose=True,
            allow_delegation=False
        )
        
        # Event Classification Agent
        self.classifier_agent = Agent(
            role='HVAC Event Classification Expert',
            goal='Classify calendar events into proper HVAC categories',
            backstory="""
            You specialize in categorizing HVAC events into:
            - Serwis (czerwony) - maintenance, repairs, emergency calls
            - Nowa Instalacja (niebieski) - new installations, major upgrades
            - Oględziny (zielony) - inspections, consultations, quotes
            """,
            llm=self.llm,
            verbose=True,
            allow_delegation=False
        )
    
    async def structure_calendar_events(self, unstructured_events: List[Dict]) -> List[Dict]:
        """Structure poorly organized calendar events"""
        try:
            logger.info(f"🗓️ Structuring {len(unstructured_events)} calendar events")
            
            structured_events = []
            
            for event in unstructured_events:
                try:
                    # Analyze and structure individual event
                    structured_event = await self._structure_single_event(event)
                    if structured_event:
                        structured_events.append(structured_event)
                        
                except Exception as e:
                    logger.error(f"Failed to structure event: {e}")
                    continue
            
            # Optimize scheduling
            optimized_events = await self._optimize_schedule(structured_events)
            
            logger.info(f"✅ Structured {len(optimized_events)} calendar events")
            return optimized_events
            
        except Exception as e:
            logger.error(f"❌ Calendar structuring failed: {e}")
            return []
    
    async def _structure_single_event(self, event: Dict) -> Optional[Dict]:
        """Structure a single calendar event"""
        try:
            # Extract event content
            event_content = self._extract_event_content(event)
            
            # Classify event
            classification = await self._classify_event(event_content)
            
            # Extract details
            details = await self._extract_event_details(event_content, classification)
            
            # Build structured event
            structured_event = {
                "original_event": event,
                "title": self._generate_smart_title(classification, details),
                "category": classification.get('category', 'Serwis'),
                "color": self._get_category_color(classification.get('category', 'Serwis')),
                "start_date": details.get('suggested_date', self._get_default_date()),
                "start_time": details.get('suggested_time', '09:00'),
                "duration_hours": details.get('duration_hours', 2),
                "customer_info": details.get('customer_info', {}),
                "location": details.get('location', 'Do ustalenia'),
                "description": details.get('description', ''),
                "priority": classification.get('priority', 'medium'),
                "required_skills": details.get('required_skills', []),
                "equipment_needed": details.get('equipment_needed', []),
                "estimated_revenue": details.get('estimated_revenue', 0),
                "ai_insights": {
                    "structured_by_ai": True,
                    "classification": classification,
                    "confidence": classification.get('confidence', 0.5),
                    "optimization_suggestions": details.get('optimization_suggestions', []),
                    "seasonal_considerations": details.get('seasonal_considerations', ''),
                    "structured_date": datetime.now().isoformat()
                }
            }
            
            return structured_event
            
        except Exception as e:
            logger.error(f"Failed to structure single event: {e}")
            return None
    
    def _extract_event_content(self, event: Dict) -> str:
        """Extract content from unstructured event"""
        content_parts = []
        
        # Event title/subject
        if event.get('title') or event.get('subject'):
            content_parts.append(f"Title: {event.get('title') or event.get('subject')}")
        
        # Event description/body
        if event.get('description') or event.get('body'):
            content_parts.append(f"Description: {event.get('description') or event.get('body')}")
        
        # Location
        if event.get('location'):
            content_parts.append(f"Location: {event.get('location')}")
        
        # Attendees/participants
        if event.get('attendees'):
            content_parts.append(f"Attendees: {', '.join(event.get('attendees', []))}")
        
        # Original date/time if available
        if event.get('date') or event.get('start_time'):
            content_parts.append(f"Original timing: {event.get('date', '')} {event.get('start_time', '')}")
        
        return "\n".join(content_parts)
    
    async def _classify_event(self, event_content: str) -> Dict:
        """Classify calendar event using AI"""
        try:
            task = Task(
                description=f"""
                Classify this HVAC calendar event into appropriate category:
                
                Event Content:
                {event_content}
                
                Classify into one of three categories:
                - Serwis (czerwony): maintenance, repairs, emergency calls, service visits
                - Nowa Instalacja (niebieski): new installations, major system upgrades
                - Oględziny (zielony): inspections, consultations, quotes, site surveys
                
                Provide classification in JSON format:
                {{
                    "category": "Serwis|Nowa Instalacja|Oględziny",
                    "priority": "urgent|high|medium|low",
                    "confidence": 0.0-1.0,
                    "reasoning": "explanation for classification",
                    "hvac_keywords": ["detected HVAC terms"],
                    "urgency_indicators": ["signals of urgency"]
                }}
                """,
                agent=self.classifier_agent,
                expected_output="JSON classification object"
            )
            
            crew = Crew(
                agents=[self.classifier_agent],
                tasks=[task],
                verbose=False
            )
            
            result = crew.kickoff()
            return self._parse_json_result(result)
            
        except Exception as e:
            logger.error(f"Event classification failed: {e}")
            return {"category": "Serwis", "priority": "medium", "confidence": 0.3}
    
    async def _extract_event_details(self, event_content: str, classification: Dict) -> Dict:
        """Extract detailed event information"""
        try:
            task = Task(
                description=f"""
                Extract detailed information from this HVAC calendar event:
                
                Event Content:
                {event_content}
                
                Classification: {classification.get('category', 'Serwis')}
                
                Extract details in JSON format:
                {{
                    "suggested_date": "YYYY-MM-DD or 'not_specified'",
                    "suggested_time": "HH:MM or 'not_specified'",
                    "duration_hours": 1-8,
                    "customer_info": {{
                        "name": "customer name if available",
                        "phone": "phone number if available",
                        "email": "email if available"
                    }},
                    "location": "full address or 'Do ustalenia'",
                    "description": "structured description in Polish",
                    "required_skills": ["technician skills needed"],
                    "equipment_needed": ["tools/equipment required"],
                    "estimated_revenue": "estimated value in PLN",
                    "optimization_suggestions": ["scheduling optimization tips"],
                    "seasonal_considerations": "seasonal factors for Warsaw climate"
                }}
                
                Consider Warsaw-specific factors:
                - Traffic patterns (avoid rush hours 7-9, 16-18)
                - Seasonal HVAC demand
                - Building accessibility in Warsaw districts
                """,
                agent=self.calendar_agent,
                expected_output="JSON event details object"
            )
            
            crew = Crew(
                agents=[self.calendar_agent],
                tasks=[task],
                verbose=False
            )
            
            result = crew.kickoff()
            return self._parse_json_result(result)
            
        except Exception as e:
            logger.error(f"Event detail extraction failed: {e}")
            return {}
    
    async def _optimize_schedule(self, events: List[Dict]) -> List[Dict]:
        """Optimize event scheduling for efficiency"""
        try:
            # Group events by date
            events_by_date = {}
            for event in events:
                date = event.get('start_date', self._get_default_date())
                if date not in events_by_date:
                    events_by_date[date] = []
                events_by_date[date].append(event)
            
            optimized_events = []
            
            # Optimize each day
            for date, day_events in events_by_date.items():
                optimized_day = await self._optimize_daily_schedule(day_events)
                optimized_events.extend(optimized_day)
            
            return optimized_events
            
        except Exception as e:
            logger.error(f"Schedule optimization failed: {e}")
            return events
    
    async def _optimize_daily_schedule(self, day_events: List[Dict]) -> List[Dict]:
        """Optimize events for a single day"""
        try:
            # Sort by priority and location
            priority_order = {'urgent': 0, 'high': 1, 'medium': 2, 'low': 3}
            
            day_events.sort(key=lambda x: (
                priority_order.get(x.get('priority', 'medium'), 2),
                x.get('location', ''),
                x.get('start_time', '09:00')
            ))
            
            # Adjust times to avoid conflicts and optimize routing
            current_time = datetime.strptime('08:00', '%H:%M')
            
            for event in day_events:
                # Set optimized start time
                event['start_time'] = current_time.strftime('%H:%M')
                event['optimized_by_ai'] = True
                
                # Add travel time buffer
                duration = event.get('duration_hours', 2)
                travel_buffer = 0.5  # 30 minutes travel time
                
                current_time += timedelta(hours=duration + travel_buffer)
                
                # Don't schedule after 17:00
                if current_time.hour >= 17:
                    break
            
            return day_events
            
        except Exception as e:
            logger.error(f"Daily schedule optimization failed: {e}")
            return day_events
    
    def _generate_smart_title(self, classification: Dict, details: Dict) -> str:
        """Generate intelligent event title"""
        category = classification.get('category', 'Serwis')
        customer_name = details.get('customer_info', {}).get('name', 'Klient')
        
        # Clean customer name
        if customer_name and customer_name != 'Klient':
            customer_name = customer_name.split()[0]  # First name only
        
        title_templates = {
            'Serwis': f"🔧 Serwis - {customer_name}",
            'Nowa Instalacja': f"🏗️ Instalacja - {customer_name}",
            'Oględziny': f"🔍 Oględziny - {customer_name}"
        }
        
        return title_templates.get(category, f"HVAC - {customer_name}")
    
    def _get_category_color(self, category: str) -> str:
        """Get color for calendar category"""
        color_map = {
            'Serwis': '#ff4d4f',           # Red
            'Nowa Instalacja': '#1890ff',  # Blue
            'Oględziny': '#52c41a'         # Green
        }
        return color_map.get(category, '#ff4d4f')
    
    def _get_default_date(self) -> str:
        """Get default date (tomorrow)"""
        tomorrow = datetime.now() + timedelta(days=1)
        return tomorrow.strftime('%Y-%m-%d')
    
    def _parse_json_result(self, result) -> Dict:
        """Parse JSON result from AI agent"""
        try:
            result_str = str(result)
            
            # Find JSON content
            start_idx = result_str.find('{')
            end_idx = result_str.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = result_str[start_idx:end_idx]
                return json.loads(json_str)
            
            return {}
            
        except Exception as e:
            logger.error(f"JSON parsing error: {e}")
            return {}
