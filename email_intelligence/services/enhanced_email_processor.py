"""
Enhanced Email Processing Service - 2025 Edition
Advanced email processing with async capabilities, M4A transcription, and intelligent parsing
"""
import asyncio
import email
import imaplib2
import ssl
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
import base64
import os
import tempfile

# Enhanced email processing libraries
from email_validator import validate_email, EmailNotValidError
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
import mailparser
import httpx

# Audio processing for M4A transcription
import whisper
import librosa
import soundfile as sf

from config import config, get_email_accounts_config
from utils.logger import setup_logger

logger = setup_logger(__name__)


@dataclass
class EnhancedEmailData:
    """Enhanced email data structure with transcription support"""
    message_id: str
    sender: str
    recipient: str
    subject: str
    body: str
    html_body: Optional[str]
    timestamp: datetime
    attachments: List[Dict[str, Any]]
    transcriptions: List[Dict[str, Any]]
    thread_id: Optional[str]
    email_type: str  # 'customer' or 'transcription'
    processing_metadata: Dict[str, Any]


class EnhancedEmailProcessor:
    """Enhanced email processor with 2025 capabilities"""
    
    def __init__(self, email_accounts_config: Dict[str, Dict]):
        self.email_accounts = email_accounts_config
        self.whisper_model = None
        self.connections = {}
        self.processing_stats = {
            'emails_processed': 0,
            'transcriptions_created': 0,
            'errors_encountered': 0,
            'last_processing_time': None
        }
        self._setup_enhanced_services()
    
    def _setup_enhanced_services(self):
        """Setup enhanced email processing services"""
        try:
            # Initialize Whisper model for M4A transcription
            self.whisper_model = whisper.load_model(config.WHISPER_MODEL)
            logger.info(f"✅ Whisper model loaded: {config.WHISPER_MODEL}")
            
            # Setup email connections
            self._setup_email_connections()
            
            logger.info("✅ Enhanced email processing services initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to setup enhanced email services: {e}")
            raise
    
    def _setup_email_connections(self):
        """Setup enhanced IMAP connections with SSL"""
        for account_name, account_config in self.email_accounts.items():
            try:
                # Create SSL context
                ssl_context = ssl.create_default_context()
                ssl_context.check_hostname = False
                ssl_context.verify_mode = ssl.CERT_NONE
                
                # Create IMAP connection
                connection = imaplib2.IMAP4_SSL(
                    account_config['imap_server'],
                    account_config['imap_port'],
                    ssl_context=ssl_context
                )
                
                # Login
                connection.login(
                    account_config['email'],
                    account_config['password']
                )
                
                self.connections[account_name] = {
                    'connection': connection,
                    'config': account_config,
                    'last_used': datetime.now()
                }
                
                logger.info(f"✅ Email connection established: {account_name}")
                
            except Exception as e:
                logger.error(f"❌ Failed to setup email connection for {account_name}: {e}")
                self.connections[account_name] = None
    
    async def fetch_all_emails_enhanced(self, limit: int = 50) -> List[EnhancedEmailData]:
        """Fetch emails from all accounts with enhanced processing"""
        all_emails = []
        
        for account_name, connection_data in self.connections.items():
            if connection_data is None:
                continue
                
            try:
                account_emails = await self._fetch_account_emails_enhanced(
                    account_name, connection_data, limit
                )
                all_emails.extend(account_emails)
                
                logger.info(f"📧 Fetched {len(account_emails)} emails from {account_name}")
                
            except Exception as e:
                logger.error(f"❌ Failed to fetch emails from {account_name}: {e}")
                self.processing_stats['errors_encountered'] += 1
        
        # Sort by timestamp (newest first)
        all_emails.sort(key=lambda x: x.timestamp, reverse=True)
        
        self.processing_stats['emails_processed'] += len(all_emails)
        self.processing_stats['last_processing_time'] = datetime.now()
        
        logger.info(f"📊 Total emails fetched: {len(all_emails)}")
        return all_emails
    
    async def _fetch_account_emails_enhanced(
        self, 
        account_name: str, 
        connection_data: Dict, 
        limit: int
    ) -> List[EnhancedEmailData]:
        """Fetch emails from specific account with enhanced parsing"""
        emails = []
        
        try:
            connection = connection_data['connection']
            account_config = connection_data['config']
            
            # Select INBOX
            connection.select('INBOX')
            
            # Search for recent emails
            since_date = (datetime.now() - timedelta(days=7)).strftime('%d-%b-%Y')
            search_criteria = f'(SINCE "{since_date}")'
            
            typ, message_numbers = connection.search(None, search_criteria)
            
            if typ != 'OK':
                logger.warning(f"Search failed for {account_name}")
                return emails
            
            # Get message numbers (limit to recent emails)
            message_nums = message_numbers[0].split()[-limit:]
            
            for num in message_nums:
                try:
                    email_data = await self._process_single_email_enhanced(
                        connection, num, account_config
                    )
                    if email_data:
                        emails.append(email_data)
                        
                except Exception as e:
                    logger.error(f"Failed to process email {num}: {e}")
                    continue
            
            return emails
            
        except Exception as e:
            logger.error(f"❌ Failed to fetch emails from {account_name}: {e}")
            return emails
    
    async def _process_single_email_enhanced(
        self, 
        connection, 
        message_num: bytes, 
        account_config: Dict
    ) -> Optional[EnhancedEmailData]:
        """Process single email with enhanced parsing and transcription"""
        try:
            # Fetch email
            typ, message_data = connection.fetch(message_num, '(RFC822)')
            
            if typ != 'OK':
                return None
            
            # Parse email with enhanced parser
            raw_email = message_data[0][1]
            parsed_email = mailparser.parse_from_bytes(raw_email)
            
            # Extract basic information
            sender = self._extract_sender(parsed_email)
            recipient = self._extract_recipient(parsed_email)
            subject = parsed_email.subject or "No Subject"
            body = self._extract_body(parsed_email)
            html_body = self._extract_html_body(parsed_email)
            timestamp = self._extract_timestamp(parsed_email)
            
            # Process attachments and transcriptions
            attachments, transcriptions = await self._process_attachments_enhanced(
                parsed_email, account_config
            )
            
            # Generate thread ID
            thread_id = self._generate_thread_id(parsed_email)
            
            # Determine email type
            email_type = account_config.get('type', 'customer')
            
            # Create processing metadata
            processing_metadata = {
                'account_name': account_config['email'],
                'processing_time': datetime.now().isoformat(),
                'has_attachments': len(attachments) > 0,
                'has_transcriptions': len(transcriptions) > 0,
                'enhanced_processing': True
            }
            
            return EnhancedEmailData(
                message_id=parsed_email.message_id or f"generated_{message_num.decode()}",
                sender=sender,
                recipient=recipient,
                subject=subject,
                body=body,
                html_body=html_body,
                timestamp=timestamp,
                attachments=attachments,
                transcriptions=transcriptions,
                thread_id=thread_id,
                email_type=email_type,
                processing_metadata=processing_metadata
            )
            
        except Exception as e:
            logger.error(f"❌ Failed to process single email: {e}")
            return None
    
    async def _process_attachments_enhanced(
        self, 
        parsed_email, 
        account_config: Dict
    ) -> Tuple[List[Dict], List[Dict]]:
        """Process attachments with M4A transcription support"""
        attachments = []
        transcriptions = []
        
        try:
            if not hasattr(parsed_email, 'attachments') or not parsed_email.attachments:
                return attachments, transcriptions
            
            for attachment in parsed_email.attachments:
                try:
                    attachment_info = {
                        'filename': attachment.get('filename', 'unknown'),
                        'content_type': attachment.get('mail_content_type', 'unknown'),
                        'size': len(attachment.get('payload', b'')),
                        'processed_at': datetime.now().isoformat()
                    }
                    
                    attachments.append(attachment_info)
                    
                    # Process M4A files for transcription
                    if (attachment_info['filename'].lower().endswith('.m4a') and 
                        account_config.get('type') == 'transcription'):
                        
                        transcription = await self._transcribe_m4a_enhanced(attachment)
                        if transcription:
                            transcriptions.append(transcription)
                            self.processing_stats['transcriptions_created'] += 1
                    
                except Exception as e:
                    logger.error(f"Failed to process attachment: {e}")
                    continue
            
            return attachments, transcriptions
            
        except Exception as e:
            logger.error(f"❌ Failed to process attachments: {e}")
            return attachments, transcriptions
    
    async def _transcribe_m4a_enhanced(self, attachment: Dict) -> Optional[Dict]:
        """Enhanced M4A transcription with Whisper"""
        try:
            if not self.whisper_model:
                logger.warning("Whisper model not available for transcription")
                return None
            
            # Save attachment to temporary file
            with tempfile.NamedTemporaryFile(suffix='.m4a', delete=False) as temp_file:
                temp_file.write(attachment.get('payload', b''))
                temp_file_path = temp_file.name
            
            try:
                # Convert M4A to WAV for better processing
                audio_data, sample_rate = librosa.load(temp_file_path, sr=16000)
                
                # Save as WAV
                wav_path = temp_file_path.replace('.m4a', '.wav')
                sf.write(wav_path, audio_data, sample_rate)
                
                # Transcribe with Whisper
                result = self.whisper_model.transcribe(
                    wav_path,
                    language='pl',  # Polish language
                    task='transcribe'
                )
                
                transcription_data = {
                    'filename': attachment.get('filename', 'unknown.m4a'),
                    'text': result['text'],
                    'language': result.get('language', 'pl'),
                    'confidence': self._calculate_transcription_confidence(result),
                    'duration': len(audio_data) / sample_rate,
                    'segments': result.get('segments', []),
                    'transcribed_at': datetime.now().isoformat(),
                    'model_used': config.WHISPER_MODEL,
                    'enhanced_processing': True
                }
                
                logger.info(f"✅ Transcribed M4A: {attachment.get('filename')}")
                return transcription_data
                
            finally:
                # Cleanup temporary files
                for file_path in [temp_file_path, wav_path]:
                    try:
                        if os.path.exists(file_path):
                            os.unlink(file_path)
                    except Exception:
                        pass
            
        except Exception as e:
            logger.error(f"❌ M4A transcription failed: {e}")
            return None

    def _extract_sender(self, parsed_email) -> str:
        """Extract sender with enhanced parsing"""
        try:
            if hasattr(parsed_email, 'from_') and parsed_email.from_:
                return parsed_email.from_[0] if isinstance(parsed_email.from_, list) else str(parsed_email.from_)
            return "<EMAIL>"
        except Exception:
            return "<EMAIL>"

    def _extract_recipient(self, parsed_email) -> str:
        """Extract recipient with enhanced parsing"""
        try:
            if hasattr(parsed_email, 'to') and parsed_email.to:
                return parsed_email.to[0] if isinstance(parsed_email.to, list) else str(parsed_email.to)
            return "<EMAIL>"
        except Exception:
            return "<EMAIL>"

    def _extract_body(self, parsed_email) -> str:
        """Extract email body with enhanced text processing"""
        try:
            # Try to get plain text body
            if hasattr(parsed_email, 'text_plain') and parsed_email.text_plain:
                body_text = parsed_email.text_plain[0] if isinstance(parsed_email.text_plain, list) else parsed_email.text_plain
                return self._clean_email_text(body_text)

            # Fallback to body attribute
            if hasattr(parsed_email, 'body') and parsed_email.body:
                return self._clean_email_text(parsed_email.body)

            return ""

        except Exception as e:
            logger.error(f"Failed to extract email body: {e}")
            return ""

    def _extract_html_body(self, parsed_email) -> Optional[str]:
        """Extract HTML body if available"""
        try:
            if hasattr(parsed_email, 'text_html') and parsed_email.text_html:
                return parsed_email.text_html[0] if isinstance(parsed_email.text_html, list) else parsed_email.text_html
            return None
        except Exception:
            return None

    def _extract_timestamp(self, parsed_email) -> datetime:
        """Extract timestamp with enhanced parsing"""
        try:
            if hasattr(parsed_email, 'date') and parsed_email.date:
                return parsed_email.date
            return datetime.now()
        except Exception:
            return datetime.now()

    def _generate_thread_id(self, parsed_email) -> Optional[str]:
        """Generate thread ID for email threading"""
        try:
            # Use message ID or subject for threading
            if hasattr(parsed_email, 'message_id') and parsed_email.message_id:
                return f"thread_{hash(parsed_email.message_id) % 1000000}"

            if hasattr(parsed_email, 'subject') and parsed_email.subject:
                return f"thread_{hash(parsed_email.subject) % 1000000}"

            return None

        except Exception:
            return None

    def _clean_email_text(self, text: str) -> str:
        """Clean email text with enhanced processing"""
        try:
            if not text:
                return ""

            # Remove common email artifacts
            lines = text.split('\n')
            cleaned_lines = []

            for line in lines:
                line = line.strip()

                # Skip empty lines and common email artifacts
                if (not line or
                    line.startswith('>') or  # Quoted text
                    line.startswith('On ') or  # Email headers
                    line.startswith('From:') or
                    line.startswith('Sent:') or
                    line.startswith('To:') or
                    'wrote:' in line.lower()):
                    continue

                cleaned_lines.append(line)

            return '\n'.join(cleaned_lines)

        except Exception as e:
            logger.error(f"Failed to clean email text: {e}")
            return text

    def _calculate_transcription_confidence(self, whisper_result: Dict) -> float:
        """Calculate transcription confidence score"""
        try:
            if 'segments' in whisper_result:
                # Calculate average confidence from segments
                confidences = []
                for segment in whisper_result['segments']:
                    if 'avg_logprob' in segment:
                        # Convert log probability to confidence (0-1)
                        confidence = min(1.0, max(0.0, (segment['avg_logprob'] + 1.0)))
                        confidences.append(confidence)

                if confidences:
                    return sum(confidences) / len(confidences)

            # Fallback confidence based on text length and quality
            text = whisper_result.get('text', '')
            if len(text) > 50:
                return 0.8
            elif len(text) > 20:
                return 0.6
            else:
                return 0.4

        except Exception as e:
            logger.error(f"Failed to calculate transcription confidence: {e}")
            return 0.5

    async def validate_email_address(self, email_address: str) -> bool:
        """Validate email address with enhanced validation"""
        try:
            # Use email-validator library
            valid = validate_email(email_address)
            return True
        except EmailNotValidError:
            return False
        except Exception as e:
            logger.error(f"Email validation error: {e}")
            return False

    async def get_processing_stats(self) -> Dict[str, Any]:
        """Get enhanced processing statistics"""
        return {
            'emails_processed': self.processing_stats['emails_processed'],
            'transcriptions_created': self.processing_stats['transcriptions_created'],
            'errors_encountered': self.processing_stats['errors_encountered'],
            'last_processing_time': self.processing_stats['last_processing_time'],
            'active_connections': len([c for c in self.connections.values() if c is not None]),
            'total_accounts': len(self.email_accounts),
            'whisper_model_loaded': self.whisper_model is not None,
            'enhanced_features_enabled': True
        }

    async def test_connections(self) -> Dict[str, bool]:
        """Test all email connections"""
        connection_status = {}

        for account_name, connection_data in self.connections.items():
            try:
                if connection_data is None:
                    connection_status[account_name] = False
                    continue

                connection = connection_data['connection']
                # Test connection with NOOP command
                typ, response = connection.noop()
                connection_status[account_name] = (typ == 'OK')

            except Exception as e:
                logger.error(f"Connection test failed for {account_name}: {e}")
                connection_status[account_name] = False

        return connection_status

    async def refresh_connections(self):
        """Refresh email connections if needed"""
        try:
            for account_name, connection_data in self.connections.items():
                if connection_data is None:
                    continue

                # Check if connection is stale (older than 1 hour)
                if (datetime.now() - connection_data['last_used']).seconds > 3600:
                    logger.info(f"Refreshing stale connection: {account_name}")

                    # Close old connection
                    try:
                        connection_data['connection'].close()
                        connection_data['connection'].logout()
                    except Exception:
                        pass

                    # Create new connection
                    account_config = connection_data['config']
                    try:
                        ssl_context = ssl.create_default_context()
                        ssl_context.check_hostname = False
                        ssl_context.verify_mode = ssl.CERT_NONE

                        new_connection = imaplib2.IMAP4_SSL(
                            account_config['imap_server'],
                            account_config['imap_port'],
                            ssl_context=ssl_context
                        )

                        new_connection.login(
                            account_config['email'],
                            account_config['password']
                        )

                        self.connections[account_name]['connection'] = new_connection
                        self.connections[account_name]['last_used'] = datetime.now()

                        logger.info(f"✅ Connection refreshed: {account_name}")

                    except Exception as e:
                        logger.error(f"❌ Failed to refresh connection {account_name}: {e}")
                        self.connections[account_name] = None

        except Exception as e:
            logger.error(f"❌ Failed to refresh connections: {e}")

    def __del__(self):
        """Cleanup connections on destruction"""
        try:
            for account_name, connection_data in self.connections.items():
                if connection_data and connection_data['connection']:
                    try:
                        connection_data['connection'].close()
                        connection_data['connection'].logout()
                    except Exception:
                        pass
        except Exception:
            pass
