"""
Enhanced AI Analysis Service - 2025 Edition
PydanticAI + CrewAI hybrid with latest NLP libraries for superior email intelligence
"""
import asyncio
import json
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime
from dataclasses import dataclass

# Enhanced AI Framework 2025
from pydantic_ai import Agent as PydanticAgent
from pydantic_ai.models import OpenAIModel
from crewai import Agent, Task, Crew
from langchain_openai import ChatOpenAI

# Enhanced NLP 2025
import spacy
from transformers import pipeline, AutoTokenizer, AutoModelForSequenceClassification
from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer
from textblob import TextBlob
import pandas as pd
import numpy as np

from config import config, AI_PROMPTS, HVAC_CATEGORIES
from utils.logger import setup_logger

logger = setup_logger(__name__)


@dataclass
class EnhancedAnalysisResult:
    """Enhanced analysis result with type safety"""
    analysis_id: str
    timestamp: str
    classification: Dict[str, Any]
    customer_insights: Dict[str, Any]
    sentiment_analysis: Dict[str, Any]
    calendar_events: List[Dict[str, Any]]
    offer_proposals: List[Dict[str, Any]]
    confidence_scores: Dict[str, float]
    processing_status: str
    ai_model_versions: Dict[str, str]


class EnhancedAIAnalyzer:
    """Enhanced AI-powered analysis service with 2025 capabilities"""
    
    def __init__(self):
        self.openai_model = None
        self.pydantic_agents = {}
        self.crewai_agents = {}
        self.nlp_models = {}
        self.sentiment_analyzers = {}
        self._setup_enhanced_ai_services()
    
    def _setup_enhanced_ai_services(self):
        """Setup enhanced AI services with latest 2025 libraries"""
        try:
            # Initialize OpenAI model for PydanticAI
            self.openai_model = OpenAIModel(
                model_name=config.OPENAI_MODEL,
                api_key=config.OPENAI_API_KEY
            )
            
            # Setup enhanced NLP models
            self._setup_nlp_models()
            
            # Setup sentiment analyzers
            self._setup_sentiment_analyzers()
            
            # Setup PydanticAI agents
            self._setup_pydantic_agents()
            
            # Setup CrewAI agents
            self._setup_crewai_agents()
            
            logger.info("✅ Enhanced AI services initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to setup enhanced AI services: {e}")
            raise
    
    def _setup_nlp_models(self):
        """Setup enhanced NLP models for 2025"""
        try:
            # Load Polish spaCy model
            try:
                self.nlp_models['polish'] = spacy.load("pl_core_news_sm")
            except OSError:
                logger.warning("Polish spaCy model not found, using English")
                self.nlp_models['polish'] = spacy.load("en_core_web_sm")
            
            # Load transformers for advanced sentiment analysis
            self.nlp_models['sentiment_transformer'] = pipeline(
                "sentiment-analysis",
                model="cardiffnlp/twitter-roberta-base-sentiment-latest",
                return_all_scores=True
            )
            
            # Load Polish BERT for classification
            try:
                self.nlp_models['polish_bert'] = pipeline(
                    "text-classification",
                    model="allegro/herbert-base-cased",
                    return_all_scores=True
                )
            except Exception:
                logger.warning("Polish BERT model not available, using multilingual")
                self.nlp_models['polish_bert'] = pipeline(
                    "text-classification",
                    model="distilbert-base-multilingual-cased",
                    return_all_scores=True
                )
            
            logger.info("✅ Enhanced NLP models loaded successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to setup NLP models: {e}")
            # Fallback to basic models
            self.nlp_models = {}
    
    def _setup_sentiment_analyzers(self):
        """Setup multiple sentiment analyzers for enhanced accuracy"""
        try:
            # VADER sentiment analyzer
            self.sentiment_analyzers['vader'] = SentimentIntensityAnalyzer()
            
            # TextBlob for additional sentiment analysis
            self.sentiment_analyzers['textblob'] = TextBlob
            
            logger.info("✅ Sentiment analyzers initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to setup sentiment analyzers: {e}")
            self.sentiment_analyzers = {}
    
    def _setup_pydantic_agents(self):
        """Setup PydanticAI agents for type-safe analysis"""
        try:
            # Email Classification Agent with PydanticAI
            self.pydantic_agents['classifier'] = PydanticAgent(
                model=self.openai_model,
                system_prompt=f"""
                You are an expert HVAC email classifier for Polish market.
                Classify emails with high accuracy and return structured data.
                
                Categories: {HVAC_CATEGORIES['service_types']}
                Priority levels: {HVAC_CATEGORIES['priority_levels']}
                
                Focus on Polish HVAC terminology and business practices.
                """,
                retries=3
            )
            
            # Customer Intelligence Agent
            self.pydantic_agents['customer_analyst'] = PydanticAgent(
                model=self.openai_model,
                system_prompt="""
                You are a customer intelligence specialist for HVAC businesses.
                Extract deep insights about customer needs and business opportunities.
                Provide actionable intelligence for sales and service teams.
                """,
                retries=3
            )
            
            logger.info("✅ PydanticAI agents initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to setup PydanticAI agents: {e}")
            self.pydantic_agents = {}
    
    def _setup_crewai_agents(self):
        """Setup CrewAI agents for collaborative analysis"""
        try:
            llm = ChatOpenAI(
                model=config.OPENAI_MODEL,
                api_key=config.OPENAI_API_KEY,
                temperature=0.1
            )
            
            # Calendar Structuring Agent
            self.crewai_agents['calendar_organizer'] = Agent(
                role='HVAC Calendar and Scheduling Optimizer',
                goal='Structure and optimize calendar events from communications',
                backstory=AI_PROMPTS['calendar_organizer'],
                llm=llm,
                verbose=True,
                allow_delegation=False
            )
            
            # Offer Generation Agent
            self.crewai_agents['offer_generator'] = Agent(
                role='HVAC Offer and Proposal Specialist',
                goal='Generate professional HVAC offers and proposals',
                backstory=AI_PROMPTS['offer_generator'],
                llm=llm,
                verbose=True,
                allow_delegation=False
            )
            
            logger.info("✅ CrewAI agents initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to setup CrewAI agents: {e}")
            self.crewai_agents = {}
    
    async def analyze_communication_enhanced(self, communication_data: Dict) -> EnhancedAnalysisResult:
        """Run enhanced AI analysis with 2025 capabilities"""
        try:
            analysis_id = str(uuid.uuid4())
            logger.info(f"🧠 Starting enhanced AI analysis: {analysis_id}")
            
            # Prepare communication content
            content = self._prepare_content(communication_data)
            
            # Run parallel enhanced analysis tasks
            tasks = [
                self._enhanced_sentiment_analysis(content),
                self._enhanced_classification(content),
                self._enhanced_customer_insights(content),
                self._enhanced_calendar_events(content),
                self._enhanced_offer_generation(content)
            ]
            
            # Execute all tasks with timeout
            results = await asyncio.wait_for(
                asyncio.gather(*tasks, return_exceptions=True),
                timeout=120  # 2 minutes timeout
            )
            
            # Compile enhanced results
            analysis_result = EnhancedAnalysisResult(
                analysis_id=analysis_id,
                timestamp=datetime.now().isoformat(),
                sentiment_analysis=results[0] if not isinstance(results[0], Exception) else {},
                classification=results[1] if not isinstance(results[1], Exception) else {},
                customer_insights=results[2] if not isinstance(results[2], Exception) else {},
                calendar_events=results[3] if not isinstance(results[3], Exception) else [],
                offer_proposals=results[4] if not isinstance(results[4], Exception) else [],
                confidence_scores=self._calculate_confidence_scores(results),
                processing_status='completed',
                ai_model_versions={
                    'openai_model': config.OPENAI_MODEL,
                    'spacy_version': spacy.__version__,
                    'transformers_version': '4.40.0',
                    'pydantic_ai_version': '0.0.12'
                }
            )
            
            # Log any errors
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Enhanced task {i} failed: {result}")
            
            logger.info(f"✅ Enhanced AI analysis completed: {analysis_id}")
            return analysis_result
            
        except Exception as e:
            logger.error(f"❌ Enhanced AI analysis failed: {e}")
            return EnhancedAnalysisResult(
                analysis_id=str(uuid.uuid4()),
                timestamp=datetime.now().isoformat(),
                classification={},
                customer_insights={},
                sentiment_analysis={},
                calendar_events=[],
                offer_proposals=[],
                confidence_scores={},
                processing_status='failed',
                ai_model_versions={}
            )
    
    def _prepare_content(self, communication_data: Dict) -> str:
        """Prepare communication content for enhanced analysis"""
        content_parts = []
        
        # Add email content with enhanced parsing
        if communication_data.get('body'):
            content_parts.append(f"Email Body:\n{communication_data['body']}")
        
        if communication_data.get('subject'):
            content_parts.append(f"Subject: {communication_data['subject']}")
        
        # Add transcription content with metadata
        if communication_data.get('transcriptions'):
            for transcription in communication_data['transcriptions']:
                content_parts.append(
                    f"Transcription ({transcription['filename']}):\n{transcription['text']}"
                )
        
        # Add sender information with enhanced parsing
        if communication_data.get('sender'):
            content_parts.append(f"Sender: {communication_data['sender']}")
        
        return "\n\n".join(content_parts)

    async def _enhanced_sentiment_analysis(self, content: str) -> Dict[str, Any]:
        """Enhanced multi-model sentiment analysis"""
        try:
            sentiment_results = {}

            # VADER sentiment analysis
            if 'vader' in self.sentiment_analyzers:
                vader_scores = self.sentiment_analyzers['vader'].polarity_scores(content)
                sentiment_results['vader'] = {
                    'compound': vader_scores['compound'],
                    'positive': vader_scores['pos'],
                    'neutral': vader_scores['neu'],
                    'negative': vader_scores['neg']
                }

            # TextBlob sentiment analysis
            if 'textblob' in self.sentiment_analyzers:
                blob = self.sentiment_analyzers['textblob'](content)
                sentiment_results['textblob'] = {
                    'polarity': blob.sentiment.polarity,
                    'subjectivity': blob.sentiment.subjectivity
                }

            # Transformer-based sentiment analysis
            if 'sentiment_transformer' in self.nlp_models:
                transformer_result = self.nlp_models['sentiment_transformer'](content[:512])
                sentiment_results['transformer'] = transformer_result[0] if transformer_result else {}

            # Calculate ensemble sentiment
            sentiment_results['ensemble'] = self._calculate_ensemble_sentiment(sentiment_results)

            # Polish-specific sentiment indicators
            sentiment_results['polish_indicators'] = self._detect_polish_sentiment_indicators(content)

            return sentiment_results

        except Exception as e:
            logger.error(f"Enhanced sentiment analysis failed: {e}")
            return {}

    async def _enhanced_classification(self, content: str) -> Dict[str, Any]:
        """Enhanced classification with multiple models"""
        try:
            classification_results = {}

            # Use PydanticAI for structured classification
            if 'classifier' in self.pydantic_agents:
                pydantic_result = await self.pydantic_agents['classifier'].run(
                    f"""
                    Classify this HVAC communication for Polish market:

                    {content}

                    Return JSON with:
                    - type: {HVAC_CATEGORIES['service_types']}
                    - priority: {HVAC_CATEGORIES['priority_levels']}
                    - urgency_score: 0.0-1.0
                    - equipment_mentioned: [list]
                    - polish_hvac_keywords: [list]
                    - confidence_score: 0.0-1.0
                    """
                )
                classification_results['pydantic'] = pydantic_result.data if pydantic_result else {}

            # Use spaCy for entity recognition
            if 'polish' in self.nlp_models:
                doc = self.nlp_models['polish'](content[:1000000])  # spaCy limit
                entities = [(ent.text, ent.label_) for ent in doc.ents]
                classification_results['entities'] = entities

            # Use BERT for additional classification
            if 'polish_bert' in self.nlp_models:
                bert_result = self.nlp_models['polish_bert'](content[:512])
                classification_results['bert'] = bert_result[0] if bert_result else {}

            # Enhanced HVAC-specific classification
            classification_results['hvac_specific'] = self._classify_hvac_specific(content)

            return classification_results

        except Exception as e:
            logger.error(f"Enhanced classification failed: {e}")
            return {}

    async def _enhanced_customer_insights(self, content: str) -> Dict[str, Any]:
        """Enhanced customer intelligence with advanced profiling"""
        try:
            insights = {}

            # Use PydanticAI for customer analysis
            if 'customer_analyst' in self.pydantic_agents:
                pydantic_insights = await self.pydantic_agents['customer_analyst'].run(
                    f"""
                    Extract customer insights from this HVAC communication:

                    {content}

                    Return JSON with:
                    - customer_needs: [list]
                    - pain_points: [list]
                    - budget_indicators: low|medium|high|premium
                    - decision_timeline: immediate|weeks|months|planning
                    - satisfaction_level: 1-10
                    - churn_risk: 0.0-1.0
                    - lifetime_value_potential: 0.0-1.0
                    - upselling_opportunities: [list]
                    """
                )
                insights['pydantic'] = pydantic_insights.data if pydantic_insights else {}

            # Advanced text analysis for customer profiling
            insights['text_analysis'] = self._analyze_customer_text_patterns(content)

            # Polish market specific insights
            insights['polish_market'] = self._analyze_polish_market_indicators(content)

            return insights

        except Exception as e:
            logger.error(f"Enhanced customer insights failed: {e}")
            return {}

    async def _enhanced_calendar_events(self, content: str) -> List[Dict[str, Any]]:
        """Enhanced calendar event extraction with AI optimization"""
        try:
            if 'calendar_organizer' not in self.crewai_agents:
                return []

            task = Task(
                description=f"""
                Extract and structure calendar events from this HVAC communication:

                {content}

                Return JSON array of events with enhanced fields:
                [
                    {{
                        "title": "Event title",
                        "category": "{HVAC_CATEGORIES['calendar_categories']}",
                        "suggested_date": "YYYY-MM-DD or 'not_specified'",
                        "suggested_time": "HH:MM or 'not_specified'",
                        "duration_hours": 1-8,
                        "location": "address or 'to_be_determined'",
                        "required_skills": [list],
                        "equipment_needed": [list],
                        "priority": "low|medium|high|urgent",
                        "confidence": 0.0-1.0,
                        "ai_generated": true,
                        "optimization_applied": true
                    }}
                ]
                """,
                agent=self.crewai_agents['calendar_organizer'],
                expected_output="JSON array of enhanced calendar events"
            )

            crew = Crew(
                agents=[self.crewai_agents['calendar_organizer']],
                tasks=[task],
                verbose=False
            )

            result = crew.kickoff()
            parsed_result = self._parse_json_result(result)
            return parsed_result if isinstance(parsed_result, list) else []

        except Exception as e:
            logger.error(f"Enhanced calendar events failed: {e}")
            return []

    async def _enhanced_offer_generation(self, content: str) -> List[Dict[str, Any]]:
        """Enhanced offer generation with market intelligence"""
        try:
            if 'offer_generator' not in self.crewai_agents:
                return []

            task = Task(
                description=f"""
                Generate enhanced HVAC offers from this communication:

                {content}

                Return JSON array of offers with market intelligence:
                [
                    {{
                        "offer_type": "installation|maintenance|repair|consultation",
                        "equipment_recommended": [list with specific models],
                        "service_description": "detailed description in Polish",
                        "estimated_price_range": "realistic PLN range",
                        "market_positioning": "competitive analysis",
                        "value_proposition": "key benefits",
                        "confidence": 0.0-1.0,
                        "ai_enhanced": true,
                        "market_intelligence": true,
                        "fulmark_optimized": true
                    }}
                ]
                """,
                agent=self.crewai_agents['offer_generator'],
                expected_output="JSON array of enhanced offers"
            )

            crew = Crew(
                agents=[self.crewai_agents['offer_generator']],
                tasks=[task],
                verbose=False
            )

            result = crew.kickoff()
            parsed_result = self._parse_json_result(result)
            return parsed_result if isinstance(parsed_result, list) else []

        except Exception as e:
            logger.error(f"Enhanced offer generation failed: {e}")
            return []

    def _calculate_ensemble_sentiment(self, sentiment_results: Dict) -> Dict[str, float]:
        """Calculate ensemble sentiment from multiple analyzers"""
        try:
            scores = []

            # VADER compound score
            if 'vader' in sentiment_results:
                scores.append(sentiment_results['vader'].get('compound', 0))

            # TextBlob polarity
            if 'textblob' in sentiment_results:
                scores.append(sentiment_results['textblob'].get('polarity', 0))

            # Transformer scores (convert to -1 to 1 scale)
            if 'transformer' in sentiment_results and sentiment_results['transformer']:
                transformer_scores = sentiment_results['transformer']
                if isinstance(transformer_scores, list) and transformer_scores:
                    # Convert transformer scores to sentiment scale
                    pos_score = next((s['score'] for s in transformer_scores if s['label'] == 'POSITIVE'), 0)
                    neg_score = next((s['score'] for s in transformer_scores if s['label'] == 'NEGATIVE'), 0)
                    scores.append(pos_score - neg_score)

            # Calculate ensemble
            if scores:
                ensemble_score = np.mean(scores)
                confidence = 1.0 - np.std(scores) if len(scores) > 1 else 0.8

                return {
                    'sentiment_score': float(ensemble_score),
                    'confidence': float(confidence),
                    'label': 'positive' if ensemble_score > 0.1 else 'negative' if ensemble_score < -0.1 else 'neutral'
                }

            return {'sentiment_score': 0.0, 'confidence': 0.0, 'label': 'neutral'}

        except Exception as e:
            logger.error(f"Ensemble sentiment calculation failed: {e}")
            return {'sentiment_score': 0.0, 'confidence': 0.0, 'label': 'neutral'}

    def _detect_polish_sentiment_indicators(self, content: str) -> Dict[str, Any]:
        """Detect Polish-specific sentiment indicators"""
        try:
            content_lower = content.lower()

            # Polish positive indicators
            positive_indicators = [
                'dziękuję', 'dzięki', 'świetnie', 'doskonale', 'bardzo dobry',
                'polecam', 'zadowolony', 'zadowolona', 'super', 'fantastycznie'
            ]

            # Polish negative indicators
            negative_indicators = [
                'problem', 'awaria', 'nie działa', 'zepsuty', 'zepsuta',
                'reklamacja', 'niezadowolony', 'niezadowolona', 'źle', 'kiepsko'
            ]

            # Polish urgency indicators
            urgency_indicators = [
                'pilne', 'natychmiast', 'szybko', 'jak najszybciej',
                'awaryjnie', 'emergency', 'nagły', 'nagła'
            ]

            positive_count = sum(1 for indicator in positive_indicators if indicator in content_lower)
            negative_count = sum(1 for indicator in negative_indicators if indicator in content_lower)
            urgency_count = sum(1 for indicator in urgency_indicators if indicator in content_lower)

            return {
                'positive_indicators_count': positive_count,
                'negative_indicators_count': negative_count,
                'urgency_indicators_count': urgency_count,
                'polish_sentiment_score': (positive_count - negative_count) / max(1, positive_count + negative_count),
                'urgency_level': 'high' if urgency_count > 0 else 'medium' if negative_count > 0 else 'low'
            }

        except Exception as e:
            logger.error(f"Polish sentiment indicators detection failed: {e}")
            return {}

    def _classify_hvac_specific(self, content: str) -> Dict[str, Any]:
        """HVAC-specific classification with Polish terminology"""
        try:
            content_lower = content.lower()

            # HVAC equipment detection
            equipment_keywords = {
                'air_conditioner': ['klimatyzacja', 'klimatyzator', 'klima', 'air condition'],
                'heat_pump': ['pompa ciepła', 'pompa', 'heat pump'],
                'ventilation': ['wentylacja', 'wentylator', 'ventilation'],
                'heating': ['ogrzewanie', 'grzejnik', 'heating', 'radiator']
            }

            # Service type detection
            service_keywords = {
                'installation': ['instalacja', 'montaż', 'installation', 'install'],
                'maintenance': ['konserwacja', 'przegląd', 'maintenance', 'serwis'],
                'repair': ['naprawa', 'repair', 'awaria', 'usterka'],
                'consultation': ['konsultacja', 'doradztwo', 'consultation', 'advice']
            }

            # Brand detection
            brands = ['daikin', 'lg', 'mitsubishi', 'carrier', 'toshiba', 'panasonic']

            detected_equipment = []
            detected_services = []
            detected_brands = []

            for equipment, keywords in equipment_keywords.items():
                if any(keyword in content_lower for keyword in keywords):
                    detected_equipment.append(equipment)

            for service, keywords in service_keywords.items():
                if any(keyword in content_lower for keyword in keywords):
                    detected_services.append(service)

            for brand in brands:
                if brand in content_lower:
                    detected_brands.append(brand)

            return {
                'detected_equipment': detected_equipment,
                'detected_services': detected_services,
                'detected_brands': detected_brands,
                'hvac_relevance_score': len(detected_equipment + detected_services) / 10.0,
                'primary_equipment': detected_equipment[0] if detected_equipment else 'unknown',
                'primary_service': detected_services[0] if detected_services else 'unknown'
            }

        except Exception as e:
            logger.error(f"HVAC-specific classification failed: {e}")
            return {}

    def _analyze_customer_text_patterns(self, content: str) -> Dict[str, Any]:
        """Analyze customer text patterns for advanced profiling"""
        try:
            # Text complexity analysis
            sentences = content.split('.')
            words = content.split()

            avg_sentence_length = len(words) / max(1, len(sentences))

            # Technical vocabulary detection
            technical_terms = [
                'btu', 'cop', 'scop', 'inverter', 'r32', 'r410a',
                'efektywność', 'wydajność', 'zużycie energii'
            ]

            technical_count = sum(1 for term in technical_terms if term.lower() in content.lower())

            # Budget indicators
            budget_keywords = {
                'low': ['tani', 'najtańszy', 'budżet', 'oszczędność'],
                'medium': ['rozsądny', 'średni', 'standardowy'],
                'high': ['premium', 'najlepszy', 'wysokiej jakości', 'drogi'],
                'premium': ['luksusowy', 'top', 'ekskluzywny']
            }

            budget_indicators = {}
            for level, keywords in budget_keywords.items():
                budget_indicators[level] = sum(1 for keyword in keywords if keyword in content.lower())

            return {
                'text_complexity': {
                    'avg_sentence_length': avg_sentence_length,
                    'total_words': len(words),
                    'total_sentences': len(sentences)
                },
                'technical_knowledge': {
                    'technical_terms_count': technical_count,
                    'technical_score': technical_count / max(1, len(words) / 100)
                },
                'budget_indicators': budget_indicators,
                'communication_style': 'technical' if technical_count > 2 else 'casual'
            }

        except Exception as e:
            logger.error(f"Customer text pattern analysis failed: {e}")
            return {}

    def _analyze_polish_market_indicators(self, content: str) -> Dict[str, Any]:
        """Analyze Polish market specific indicators"""
        try:
            content_lower = content.lower()

            # Polish building types
            building_types = {
                'kamienica': ['kamienica', 'stara kamienica', 'przedwojenna'],
                'blok': ['blok', 'blokowisko', 'osiedle'],
                'dom_jednorodzinny': ['dom', 'domek', 'jednorodzinny'],
                'biurowiec': ['biuro', 'biurowiec', 'office'],
                'sklep': ['sklep', 'lokal', 'commercial']
            }

            # Seasonal indicators
            seasonal_keywords = {
                'heating_season': ['zima', 'zimno', 'ogrzewanie', 'mróz'],
                'cooling_season': ['lato', 'upał', 'chłodzenie', 'klimatyzacja'],
                'transition': ['wiosna', 'jesień', 'przejściowy']
            }

            detected_building = None
            for building, keywords in building_types.items():
                if any(keyword in content_lower for keyword in keywords):
                    detected_building = building
                    break

            detected_season = None
            for season, keywords in seasonal_keywords.items():
                if any(keyword in content_lower for keyword in keywords):
                    detected_season = season
                    break

            # Warsaw district detection
            warsaw_districts = [
                'mokotów', 'śródmieście', 'żoliborz', 'ochota', 'wola',
                'praga', 'bemowo', 'bielany', 'targówek', 'ursus'
            ]

            detected_district = None
            for district in warsaw_districts:
                if district in content_lower:
                    detected_district = district
                    break

            return {
                'building_type': detected_building,
                'seasonal_context': detected_season,
                'warsaw_district': detected_district,
                'polish_market_score': 1.0 if any([detected_building, detected_season, detected_district]) else 0.5
            }

        except Exception as e:
            logger.error(f"Polish market indicators analysis failed: {e}")
            return {}

    def _calculate_confidence_scores(self, results: List) -> Dict[str, float]:
        """Calculate confidence scores for analysis results"""
        try:
            confidence_scores = {}

            # Calculate confidence based on successful results
            successful_results = sum(1 for result in results if not isinstance(result, Exception))
            total_results = len(results)

            overall_confidence = successful_results / total_results if total_results > 0 else 0.0

            confidence_scores['overall'] = overall_confidence
            confidence_scores['sentiment_analysis'] = 0.9 if not isinstance(results[0], Exception) else 0.0
            confidence_scores['classification'] = 0.85 if not isinstance(results[1], Exception) else 0.0
            confidence_scores['customer_insights'] = 0.8 if not isinstance(results[2], Exception) else 0.0
            confidence_scores['calendar_events'] = 0.75 if not isinstance(results[3], Exception) else 0.0
            confidence_scores['offer_proposals'] = 0.7 if not isinstance(results[4], Exception) else 0.0

            return confidence_scores

        except Exception as e:
            logger.error(f"Confidence score calculation failed: {e}")
            return {'overall': 0.0}

    def _parse_json_result(self, result) -> Dict:
        """Parse JSON result from AI agent with enhanced error handling"""
        try:
            result_str = str(result)

            # Find JSON content (object)
            start_idx = result_str.find('{')
            end_idx = result_str.rfind('}') + 1

            if start_idx != -1 and end_idx != -1:
                json_str = result_str[start_idx:end_idx]
                return json.loads(json_str)

            # Find JSON content (array)
            start_idx = result_str.find('[')
            end_idx = result_str.rfind(']') + 1

            if start_idx != -1 and end_idx != -1:
                json_str = result_str[start_idx:end_idx]
                return json.loads(json_str)

            logger.warning(f"Could not parse JSON from result: {result_str[:200]}...")
            return {}

        except Exception as e:
            logger.error(f"Enhanced JSON parsing error: {e}")
            return {}
