"""
Enhanced Configuration for Email Intelligence System 2025
Updated with latest AI models, enhanced features, and Polish HVAC market specifics
"""
import os
from typing import Dict, List, Any
from dataclasses import dataclass


@dataclass
class EnhancedConfig:
    """Enhanced configuration class with 2025 features"""
    
    # Enhanced AI Models Configuration
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")
    OPENAI_MODEL: str = os.getenv("OPENAI_MODEL", "gpt-4-turbo-preview")
    
    # Enhanced Whisper Configuration
    WHISPER_MODEL: str = os.getenv("WHISPER_MODEL", "medium")  # base, small, medium, large
    WHISPER_LANGUAGE: str = "pl"  # Polish language
    
    # Enhanced Weaviate Configuration
    WEAVIATE_URL: str = os.getenv("WEAVIATE_URL", "http://localhost:8080")
    WEAVIATE_API_KEY: str = os.getenv("WEAVIATE_API_KEY", "")
    WEAVIATE_CLASS_PREFIX: str = "HVAC2025"
    
    # Enhanced GoSpine CRM Configuration
    GOSPINE_API_URL: str = os.getenv("GOSPINE_API_URL", "http://localhost:8080/api")
    GOSPINE_API_KEY: str = os.getenv("GOSPINE_API_KEY", "")
    
    # Enhanced Email Configuration
    EMAIL_PROCESSING_BATCH_SIZE: int = int(os.getenv("EMAIL_PROCESSING_BATCH_SIZE", "20"))
    EMAIL_PROCESSING_INTERVAL: int = int(os.getenv("EMAIL_PROCESSING_INTERVAL", "300"))  # 5 minutes
    
    # Enhanced Logging
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE: str = os.getenv("LOG_FILE", "enhanced_email_intelligence.log")
    
    # Enhanced Features Flags
    ENABLE_PYDANTIC_AI: bool = os.getenv("ENABLE_PYDANTIC_AI", "true").lower() == "true"
    ENABLE_ENHANCED_NLP: bool = os.getenv("ENABLE_ENHANCED_NLP", "true").lower() == "true"
    ENABLE_M4A_TRANSCRIPTION: bool = os.getenv("ENABLE_M4A_TRANSCRIPTION", "true").lower() == "true"
    ENABLE_POLISH_MARKET_INTELLIGENCE: bool = os.getenv("ENABLE_POLISH_MARKET_INTELLIGENCE", "true").lower() == "true"


# Enhanced configuration instance
enhanced_config = EnhancedConfig()


# Enhanced HVAC Categories for Polish Market
ENHANCED_HVAC_CATEGORIES = {
    "service_types": [
        "instalacja_klimatyzacji",
        "serwis_klimatyzacji", 
        "naprawa_klimatyzacji",
        "konserwacja_klimatyzacji",
        "instalacja_pompy_ciepla",
        "serwis_pompy_ciepla",
        "instalacja_wentylacji",
        "serwis_wentylacji",
        "audyt_energetyczny",
        "konsultacja_techniczna",
        "modernizacja_systemu",
        "wymiana_filtrów"
    ],
    
    "priority_levels": [
        "niski",      # low
        "średni",     # medium  
        "wysoki",     # high
        "pilny",      # urgent
        "awaryjny"    # emergency
    ],
    
    "calendar_categories": [
        "serwis",           # service
        "nowa_instalacja",  # new installation
        "przegląd",         # inspection
        "konsultacja",      # consultation
        "awaria",           # emergency
        "modernizacja"      # modernization
    ],
    
    "equipment_brands": [
        "daikin", "lg", "mitsubishi", "carrier", "toshiba", 
        "panasonic", "fujitsu", "gree", "haier", "samsung"
    ],
    
    "polish_building_types": [
        "kamienica", "blok", "dom_jednorodzinny", "biurowiec", 
        "sklep", "restauracja", "hotel", "szkoła", "szpital"
    ],
    
    "warsaw_districts": [
        "mokotów", "śródmieście", "żoliborz", "ochota", "wola",
        "praga_północ", "praga_południe", "bemowo", "bielany", 
        "targówek", "ursus", "ursynów", "wilanów", "włochy"
    ]
}


# Enhanced AI Prompts for Polish HVAC Market
ENHANCED_AI_PROMPTS = {
    "email_classifier": """
    Jesteś ekspertem w klasyfikacji emaili HVAC dla polskiego rynku.
    Analizujesz komunikację klientów firmy HVAC w Warszawie.
    
    Klasyfikuj emaile według:
    - Typ usługi: {service_types}
    - Priorytet: {priority_levels}
    - Typ budynku: {building_types}
    - Dzielnica Warszawy: {districts}
    
    Zwróć uwagę na polską terminologię HVAC i lokalne uwarunkowania.
    """,
    
    "sentiment_analyzer": """
    Analizujesz sentyment komunikacji klientów HVAC w Polsce.
    
    Zwróć uwagę na:
    - Polskie wskaźniki zadowolenia/niezadowolenia
    - Pilność zgłoszenia (słowa kluczowe: pilne, natychmiast, awaria)
    - Poziom technicznej wiedzy klienta
    - Wskaźniki budżetowe (tani, premium, oszczędność)
    
    Uwzględnij kulturowe aspekty komunikacji w Polsce.
    """,
    
    "customer_profiler": """
    Tworzysz profile klientów HVAC na polskim rynku.
    
    Analizuj:
    - Typ klienta (prywatny/biznesowy)
    - Poziom technicznej wiedzy
    - Wskaźniki budżetowe
    - Preferencje marki (Daikin, LG, Mitsubishi)
    - Typ budynku i lokalizacja w Warszawie
    - Sezonowość potrzeb (zima/lato)
    
    Generuj actionable insights dla zespołu sprzedaży.
    """,
    
    "calendar_organizer": """
    Organizujesz kalendarz serwisu HVAC w Warszawie.
    
    Uwzględnij:
    - Kategorie wizyt: {calendar_categories}
    - Czas dojazdu między dzielnicami Warszawy
    - Sezonowość (zima = ogrzewanie, lato = klimatyzacja)
    - Priorytet awarii vs. planowane serwisy
    - Dostępność specjalistycznych narzędzi
    
    Optymalizuj trasę i efektywność zespołu.
    """,
    
    "offer_generator": """
    Generujesz oferty HVAC dla polskiego rynku.
    
    Uwzględnij:
    - Ceny w PLN (realistyczne dla polskiego rynku)
    - Popularne marki: Daikin, LG, Mitsubishi
    - Typ budynku i jego specyfikę
    - Sezonowe promocje i rabaty
    - Konkurencyjność na rynku warszawskim
    - Aspekty prawne i certyfikaty
    
    Generuj profesjonalne oferty w języku polskim.
    """
}


# Enhanced Email Accounts Configuration
def get_enhanced_email_accounts_config() -> Dict[str, Dict]:
    """Get enhanced email accounts configuration"""
    return {
        "dolores_transcriptions": {
            "email": "<EMAIL>",
            "password": "Blaeritipol1",
            "imap_server": "imap.gmail.com",
            "imap_port": 993,
            "type": "transcription",
            "enhanced_processing": True,
            "m4a_transcription": True,
            "description": "Konto do transkrypcji nagrań audio (M4A)"
        },
        "grzegorz_customer": {
            "email": "<EMAIL>", 
            "password": "Blaeritipol1",
            "imap_server": "imap.gmail.com",
            "imap_port": 993,
            "type": "customer",
            "enhanced_processing": True,
            "customer_intelligence": True,
            "description": "Konto do komunikacji z klientami"
        }
    }


# Enhanced Weaviate Schema for 2025
ENHANCED_WEAVIATE_SCHEMA = {
    "classes": [
        {
            "class": "HVACCommunication2025",
            "description": "Enhanced HVAC communication with 2025 AI capabilities",
            "vectorizer": "text2vec-openai",
            "moduleConfig": {
                "text2vec-openai": {
                    "model": "text-embedding-3-small",
                    "modelVersion": "002",
                    "type": "text"
                }
            },
            "properties": [
                {
                    "name": "content",
                    "dataType": ["text"],
                    "description": "Communication content"
                },
                {
                    "name": "sender",
                    "dataType": ["string"],
                    "description": "Sender email address"
                },
                {
                    "name": "subject",
                    "dataType": ["string"],
                    "description": "Email subject"
                },
                {
                    "name": "timestamp",
                    "dataType": ["date"],
                    "description": "Communication timestamp"
                },
                {
                    "name": "classification",
                    "dataType": ["object"],
                    "description": "Enhanced AI classification results"
                },
                {
                    "name": "sentiment_analysis",
                    "dataType": ["object"],
                    "description": "Multi-model sentiment analysis"
                },
                {
                    "name": "customer_insights",
                    "dataType": ["object"],
                    "description": "Advanced customer profiling"
                },
                {
                    "name": "transcriptions",
                    "dataType": ["object[]"],
                    "description": "M4A transcription results"
                },
                {
                    "name": "polish_market_indicators",
                    "dataType": ["object"],
                    "description": "Polish HVAC market specific indicators"
                },
                {
                    "name": "confidence_scores",
                    "dataType": ["object"],
                    "description": "Enhanced confidence scoring"
                },
                {
                    "name": "ai_model_versions",
                    "dataType": ["object"],
                    "description": "AI model versions used"
                }
            ]
        },
        {
            "class": "HVACCustomer2025",
            "description": "Enhanced HVAC customer profile with 2025 intelligence",
            "vectorizer": "text2vec-openai",
            "properties": [
                {
                    "name": "email",
                    "dataType": ["string"],
                    "description": "Customer email address"
                },
                {
                    "name": "profile_data",
                    "dataType": ["object"],
                    "description": "Enhanced customer profile"
                },
                {
                    "name": "communication_history",
                    "dataType": ["object[]"],
                    "description": "Communication history analysis"
                },
                {
                    "name": "preferences",
                    "dataType": ["object"],
                    "description": "Customer preferences and behavior"
                },
                {
                    "name": "polish_market_profile",
                    "dataType": ["object"],
                    "description": "Polish market specific profile data"
                }
            ]
        }
    ]
}


# Enhanced Performance Metrics
ENHANCED_PERFORMANCE_TARGETS = {
    "email_processing_speed": 5.0,  # seconds per email
    "transcription_accuracy": 0.85,  # 85% accuracy target
    "sentiment_confidence": 0.90,    # 90% confidence target
    "classification_accuracy": 0.88,  # 88% accuracy target
    "customer_insight_relevance": 0.92,  # 92% relevance target
    "polish_market_relevance": 0.94,     # 94% Polish market relevance
    "system_uptime": 0.99,               # 99% uptime target
    "api_response_time": 2.0             # 2 seconds max response time
}


# Enhanced Feature Flags
ENHANCED_FEATURES = {
    "pydantic_ai_hybrid": True,
    "multi_model_sentiment": True,
    "m4a_transcription": True,
    "polish_nlp": True,
    "advanced_customer_profiling": True,
    "real_time_collaboration": True,
    "ensemble_confidence_scoring": True,
    "semantic_search_v4": True,
    "calendar_optimization": True,
    "offer_intelligence": True
}
