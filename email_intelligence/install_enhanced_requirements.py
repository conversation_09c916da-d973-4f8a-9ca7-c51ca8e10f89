#!/usr/bin/env python3
"""
Enhanced Requirements Installation Script for Email Intelligence 2025
Installs latest AI libraries with proper dependency management
"""
import subprocess
import sys
import os
from typing import List, Dict
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class EnhancedRequirementsInstaller:
    """Enhanced requirements installer with dependency management"""
    
    def __init__(self):
        self.python_executable = sys.executable
        self.requirements_installed = []
        self.failed_installations = []
    
    def install_enhanced_requirements(self):
        """Install enhanced requirements with proper order"""
        logger.info("🚀 Starting Enhanced Email Intelligence 2025 requirements installation...")
        
        # Phase 1: Core dependencies
        core_requirements = [
            "pip>=24.0",
            "setuptools>=69.0.0",
            "wheel>=0.42.0"
        ]
        
        # Phase 2: Enhanced AI Framework
        ai_requirements = [
            "pydantic-ai==0.0.12",
            "crewai==0.51.0", 
            "langchain==0.1.20",
            "langchain-openai==0.1.3",
            "langchain-community==0.0.29",
            "openai==1.12.0"
        ]
        
        # Phase 3: Enhanced NLP
        nlp_requirements = [
            "spacy==3.7.4",
            "transformers==4.40.0",
            "sentence-transformers==2.6.1",
            "vaderSentiment==3.3.2",
            "textblob==0.18.0",
            "torch>=2.0.0"  # Required for transformers
        ]
        
        # Phase 4: Enhanced Email Processing
        email_requirements = [
            "imaplib2==3.6",
            "email-validator==2.1.1",
            "python-email-reply-parser==0.5.12",
            "mailparser==4.1.0"
        ]
        
        # Phase 5: Vector Databases
        vector_requirements = [
            "weaviate-client==4.5.4",
            "faiss-cpu==1.7.4",
            "chromadb==0.4.22",
            "qdrant-client==1.8.0"
        ]
        
        # Phase 6: Audio Processing
        audio_requirements = [
            "openai-whisper>=20231117",
            "librosa==0.10.1",
            "soundfile==0.12.1",
            "ffmpeg-python==0.2.0"
        ]
        
        # Phase 7: Web Framework
        web_requirements = [
            "fastapi==0.109.2",
            "uvicorn[standard]==0.27.1",
            "pydantic==2.6.1",
            "httpx==0.27.0",
            "python-multipart==0.0.9"
        ]
        
        # Phase 8: Data Processing
        data_requirements = [
            "pandas==2.2.1",
            "numpy==1.26.4",
            "scikit-learn==1.4.1",
            "python-dotenv==1.0.1",
            "pyyaml==6.0.1"
        ]
        
        # Phase 9: Document Processing
        doc_requirements = [
            "reportlab==4.0.9",
            "Pillow==10.2.0",
            "pypdf==4.0.1"
        ]
        
        # Phase 10: Development Tools
        dev_requirements = [
            "pytest==8.0.0",
            "pytest-asyncio==0.23.5",
            "black==24.2.0",
            "flake8==7.0.0"
        ]
        
        # Install in phases
        installation_phases = [
            ("Core Dependencies", core_requirements),
            ("Enhanced AI Framework", ai_requirements),
            ("Enhanced NLP", nlp_requirements),
            ("Enhanced Email Processing", email_requirements),
            ("Vector Databases", vector_requirements),
            ("Audio Processing", audio_requirements),
            ("Web Framework", web_requirements),
            ("Data Processing", data_requirements),
            ("Document Processing", doc_requirements),
            ("Development Tools", dev_requirements)
        ]
        
        for phase_name, requirements in installation_phases:
            logger.info(f"📦 Installing {phase_name}...")
            self._install_requirements_batch(requirements, phase_name)
        
        # Install spaCy Polish model
        self._install_spacy_models()
        
        # Verify installations
        self._verify_installations()
        
        # Generate installation report
        self._generate_installation_report()
    
    def _install_requirements_batch(self, requirements: List[str], phase_name: str):
        """Install requirements batch with error handling"""
        for requirement in requirements:
            try:
                logger.info(f"  Installing {requirement}...")
                
                # Use pip install with enhanced options
                cmd = [
                    self.python_executable, "-m", "pip", "install",
                    "--upgrade",
                    "--no-cache-dir",
                    requirement
                ]
                
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=300  # 5 minutes timeout
                )
                
                if result.returncode == 0:
                    self.requirements_installed.append(requirement)
                    logger.info(f"  ✅ {requirement} installed successfully")
                else:
                    self.failed_installations.append({
                        "requirement": requirement,
                        "phase": phase_name,
                        "error": result.stderr
                    })
                    logger.error(f"  ❌ Failed to install {requirement}: {result.stderr}")
                
            except subprocess.TimeoutExpired:
                logger.error(f"  ⏰ Timeout installing {requirement}")
                self.failed_installations.append({
                    "requirement": requirement,
                    "phase": phase_name,
                    "error": "Installation timeout"
                })
            except Exception as e:
                logger.error(f"  ❌ Error installing {requirement}: {e}")
                self.failed_installations.append({
                    "requirement": requirement,
                    "phase": phase_name,
                    "error": str(e)
                })
    
    def _install_spacy_models(self):
        """Install spaCy language models"""
        logger.info("🌍 Installing spaCy language models...")
        
        models = [
            "pl_core_news_sm",  # Polish model
            "en_core_web_sm"    # English model (fallback)
        ]
        
        for model in models:
            try:
                logger.info(f"  Installing spaCy model: {model}")
                
                cmd = [self.python_executable, "-m", "spacy", "download", model]
                
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=300
                )
                
                if result.returncode == 0:
                    logger.info(f"  ✅ spaCy model {model} installed successfully")
                else:
                    logger.warning(f"  ⚠️ Failed to install spaCy model {model}: {result.stderr}")
                
            except Exception as e:
                logger.error(f"  ❌ Error installing spaCy model {model}: {e}")
    
    def _verify_installations(self):
        """Verify critical installations"""
        logger.info("🔍 Verifying critical installations...")
        
        critical_imports = [
            ("pydantic_ai", "PydanticAI"),
            ("crewai", "CrewAI"),
            ("spacy", "spaCy"),
            ("transformers", "Transformers"),
            ("whisper", "OpenAI Whisper"),
            ("weaviate", "Weaviate Client"),
            ("fastapi", "FastAPI")
        ]
        
        verification_results = []
        
        for module_name, display_name in critical_imports:
            try:
                __import__(module_name)
                verification_results.append((display_name, True, ""))
                logger.info(f"  ✅ {display_name} verified successfully")
            except ImportError as e:
                verification_results.append((display_name, False, str(e)))
                logger.error(f"  ❌ {display_name} verification failed: {e}")
        
        self.verification_results = verification_results
    
    def _generate_installation_report(self):
        """Generate installation report"""
        logger.info("📊 Generating installation report...")
        
        report = f"""
# Enhanced Email Intelligence 2025 - Installation Report

## Installation Summary
- Total requirements processed: {len(self.requirements_installed) + len(self.failed_installations)}
- Successfully installed: {len(self.requirements_installed)}
- Failed installations: {len(self.failed_installations)}

## Successfully Installed Requirements
"""
        
        for req in self.requirements_installed:
            report += f"- ✅ {req}\n"
        
        if self.failed_installations:
            report += "\n## Failed Installations\n"
            for failure in self.failed_installations:
                report += f"- ❌ {failure['requirement']} (Phase: {failure['phase']})\n"
                report += f"  Error: {failure['error']}\n"
        
        if hasattr(self, 'verification_results'):
            report += "\n## Verification Results\n"
            for name, success, error in self.verification_results:
                status = "✅" if success else "❌"
                report += f"- {status} {name}\n"
                if not success:
                    report += f"  Error: {error}\n"
        
        report += f"""
## Next Steps
1. Run the enhanced email intelligence system: `python enhanced_main.py`
2. Check system health: `curl http://localhost:8002/health`
3. Test enhanced analysis: `curl -X POST http://localhost:8002/analyze-enhanced`

## Enhanced Features Available
- PydanticAI + CrewAI Hybrid
- Multi-Model Sentiment Analysis
- M4A Transcription with Whisper
- Polish HVAC Market Intelligence
- Advanced Customer Profiling
- Real-time Agent Collaboration

Generated at: {subprocess.run(['date'], capture_output=True, text=True).stdout.strip()}
"""
        
        # Save report
        with open("installation_report.md", "w", encoding="utf-8") as f:
            f.write(report)
        
        logger.info("📄 Installation report saved to: installation_report.md")
        
        # Print summary
        success_rate = len(self.requirements_installed) / (len(self.requirements_installed) + len(self.failed_installations)) * 100
        logger.info(f"🎯 Installation success rate: {success_rate:.1f}%")
        
        if self.failed_installations:
            logger.warning(f"⚠️ {len(self.failed_installations)} installations failed - check installation_report.md")
        else:
            logger.info("🎉 All requirements installed successfully!")


def main():
    """Main installation function"""
    installer = EnhancedRequirementsInstaller()
    installer.install_enhanced_requirements()


if __name__ == "__main__":
    main()
