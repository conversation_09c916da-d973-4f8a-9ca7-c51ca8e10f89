# Email Intelligence System Requirements - Enhanced 2025
# Core AI and ML libraries - Latest 2025 versions
openai==1.12.0
pydantic-ai==0.0.12
crewai==0.51.0
langchain==0.1.20
langchain-openai==0.1.3
langchain-community==0.0.29

# Audio processing and transcription
openai-whisper==20231117
torch==2.1.2
torchaudio==2.1.2
librosa==0.10.1
soundfile==1.0.4

# Enhanced NLP and Sentiment Analysis 2025
spacy==3.7.4
transformers==4.40.0
vaderSentiment==3.3.2
textblob==0.18.0
polish-nlp==1.2.3

# Vector database and embeddings - Enhanced 2025
weaviate-client==4.5.4
sentence-transformers==2.6.1
chromadb==0.4.22
faiss-cpu==1.7.4
qdrant-client==1.8.0

# Enhanced Email processing
imaplib2==3.6
email-validator==2.1.1
python-email-reply-parser==0.5.12
mailparser==4.1.0

# Web framework and API - Enhanced 2025
fastapi==0.109.2
uvicorn[standard]==0.27.1
pydantic==2.6.1
httpx==0.27.0
python-multipart==0.0.9

# Data processing - Enhanced 2025
pandas==2.2.1
numpy==1.26.4
scikit-learn==1.4.1
python-dotenv==1.0.1
pyyaml==6.0.1

# PDF generation and document processing - Enhanced 2025
reportlab==4.0.9
Pillow==10.2.0
pypdf==4.0.1

# Database and caching
redis==5.0.1
motor==3.3.2
pymongo==4.6.1

# Async and concurrency
asyncio==3.4.3
aiofiles==23.2.1
celery==5.3.4

# Logging and monitoring
loguru==0.7.2
prometheus-client==0.19.0

# Testing
pytest==7.4.4
pytest-asyncio==0.23.2
httpx==0.26.0

# Development tools
black==23.12.1
flake8==7.0.0
mypy==1.8.0
rich==13.7.0
