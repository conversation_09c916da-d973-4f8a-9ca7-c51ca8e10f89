#!/usr/bin/env python3
"""
Test Enhanced Email Intelligence Features
Demonstrates the fantastic new capabilities of FULMARK Email Intelligence System
"""
import asyncio
import json
from datetime import datetime
from typing import Dict, List

import httpx

# Test configuration
API_BASE_URL = "http://localhost:8001"
TEST_EMAIL_CONTENT = """
<PERSON><PERSON><PERSON> dobry,

Mam problem z klimatyzacją Daikin w moim mieszkaniu na Mokotowie. 
Urządzenie nie chłodzi prawidłowo i wydaje dziwne dźwięki.
<PERSON><PERSON> mogliby Państwo przyjechać na oględziny w tym tygodniu?

Mieszkanie znajduje się w bloku na ul. Puławskiej 123.
Preferuję wizytę w godzinach popołudniowych.

Pozdrawiam,
<PERSON>
tel: 123-456-789
"""

TEST_CALENDAR_EVENTS = [
    {
        "title": "klimatyzacja kowalski mokotów",
        "description": "naprawa daikin nie działa",
        "location": "puławska 123",
        "date": "2025-01-30"
    },
    {
        "title": "instalacja lg nowak",
        "description": "nowa klimatyzacja 3 pokoje dom",
        "location": "wilanów",
        "date": "2025-02-01"
    },
    {
        "title": "serwis mitsubishi",
        "description": "konserwacja roczna biuro",
        "location": "centrum",
        "date": "2025-01-31"
    }
]


class EnhancedFeaturesTest:
    """Test class for enhanced email intelligence features"""
    
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=60.0)
        self.base_url = API_BASE_URL
    
    async def test_system_health(self) -> bool:
        """Test if the system is running"""
        try:
            print("🔍 Testing system health...")
            response = await self.client.get(f"{self.base_url}/health")
            
            if response.status_code == 200:
                health_data = response.json()
                print(f"✅ System Status: {health_data.get('status', 'unknown')}")
                print(f"   📧 Email Processor: {health_data.get('email_processor', 'unknown')}")
                print(f"   🧠 AI Analyzer: {health_data.get('ai_analyzer', 'unknown')}")
                print(f"   🔍 Weaviate Client: {health_data.get('weaviate_client', 'unknown')}")
                print(f"   🔗 CRM Integrator: {health_data.get('crm_integrator', 'unknown')}")
                return True
            else:
                print(f"❌ Health check failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Health check error: {e}")
            return False
    
    async def test_auto_kanban_creation(self) -> bool:
        """Test automatic Kanban card creation"""
        try:
            print("\n🎯 Testing Auto Kanban Creation...")
            
            payload = {
                "email_content": TEST_EMAIL_CONTENT,
                "sender": "<EMAIL>",
                "subject": "Problem z klimatyzacją Daikin",
                "force_create": True
            }
            
            response = await self.client.post(
                f"{self.base_url}/auto-kanban",
                params=payload
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Auto Kanban Success!")
                print(f"   🎯 Kanban Cards Created: {result.get('kanban_cards_created', 0)}")
                print(f"   🤖 AI Confidence: {result.get('ai_analysis', {}).get('confidence', 0):.2f}")
                
                # Show created actions
                for action in result.get('kanban_actions', []):
                    print(f"   📋 Action: {action.get('action', 'unknown')}")
                    if 'order_number' in action:
                        print(f"      Order: {action['order_number']}")
                    if 'estimated_revenue' in action:
                        print(f"      Revenue: {action['estimated_revenue']} PLN")
                
                return True
            else:
                print(f"❌ Auto Kanban failed: {response.status_code}")
                print(f"   Error: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Auto Kanban error: {e}")
            return False
    
    async def test_calendar_structuring(self) -> bool:
        """Test calendar event structuring"""
        try:
            print("\n🗓️ Testing Calendar Structuring...")
            
            response = await self.client.post(
                f"{self.base_url}/structure-calendar",
                json=TEST_CALENDAR_EVENTS
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Calendar Structuring Success!")
                print(f"   📅 Original Events: {result.get('original_count', 0)}")
                print(f"   ✨ Structured Events: {result.get('structured_count', 0)}")
                
                # Show structured events
                for event in result.get('structured_events', [])[:3]:  # Show first 3
                    print(f"   📋 {event.get('title', 'Unknown')}")
                    print(f"      Category: {event.get('category', 'Unknown')} ({event.get('color', 'Unknown')})")
                    print(f"      Date: {event.get('start_date', 'Unknown')} at {event.get('start_time', 'Unknown')}")
                    print(f"      Duration: {event.get('duration_hours', 0)}h")
                    if event.get('ai_insights', {}).get('confidence'):
                        print(f"      AI Confidence: {event['ai_insights']['confidence']:.2f}")
                
                return True
            else:
                print(f"❌ Calendar structuring failed: {response.status_code}")
                print(f"   Error: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Calendar structuring error: {e}")
            return False
    
    async def test_pdf_proposal_generation(self) -> bool:
        """Test PDF proposal generation"""
        try:
            print("\n📄 Testing PDF Proposal Generation...")
            
            offer_data = {
                "offer_type": "installation",
                "equipment_recommended": ["Daikin FTXM35R", "Jednostka zewnętrzna RXM35R"],
                "service_description": "Instalacja klimatyzacji Daikin w mieszkaniu 3-pokojowym",
                "estimated_price_range": "8000-12000 PLN",
                "installation_timeline": "2-3 dni robocze",
                "value_proposition": "Energooszczędna klimatyzacja z 5-letnią gwarancją",
                "next_steps": [
                    "Wizyta techniczna i pomiary",
                    "Przygotowanie szczegółowej oferty",
                    "Realizacja instalacji"
                ],
                "urgency": "medium",
                "confidence": 0.85
            }
            
            customer_data = {
                "name": "Jan Kowalski",
                "email": "<EMAIL>",
                "phone": "123-456-789"
            }
            
            analysis_data = {
                "confidence": 0.85,
                "classification": {
                    "type": "installation",
                    "priority": "high"
                }
            }
            
            response = await self.client.post(
                f"{self.base_url}/generate-proposal",
                json={
                    "offer_data": offer_data,
                    "customer_data": customer_data,
                    "analysis_data": analysis_data
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ PDF Proposal Generation Success!")
                print(f"   📄 PDF Path: {result.get('pdf_path', 'Unknown')}")
                print(f"   🎨 Fulmark Branding: {result.get('fulmark_branding', False)}")
                print(f"   💼 Professional Layout: ✅")
                return True
            else:
                print(f"❌ PDF generation failed: {response.status_code}")
                print(f"   Error: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ PDF generation error: {e}")
            return False
    
    async def test_email_analysis(self) -> bool:
        """Test enhanced email analysis"""
        try:
            print("\n🧠 Testing Enhanced Email Analysis...")
            
            payload = {
                "email_content": TEST_EMAIL_CONTENT,
                "sender": "<EMAIL>",
                "subject": "Problem z klimatyzacją Daikin",
                "attachments": []
            }
            
            response = await self.client.post(
                f"{self.base_url}/analyze",
                json=payload
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Email Analysis Success!")
                print(f"   🎯 Analysis ID: {result.get('analysis_id', 'Unknown')}")
                
                # Show classification
                classification = result.get('classification', {})
                print(f"   📊 Classification:")
                print(f"      Type: {classification.get('type', 'Unknown')}")
                print(f"      Priority: {classification.get('priority', 'Unknown')}")
                print(f"      Confidence: {classification.get('confidence_score', 0):.2f}")
                
                # Show customer insights
                insights = result.get('customer_insights', {})
                print(f"   👤 Customer Insights:")
                print(f"      Budget: {insights.get('budget_indicators', 'Unknown')}")
                print(f"      Timeline: {insights.get('decision_timeline', 'Unknown')}")
                
                # Show calendar events
                events = result.get('calendar_events', [])
                print(f"   📅 Calendar Events Generated: {len(events)}")
                
                # Show offers
                offers = result.get('offer_proposals', [])
                print(f"   💼 Offer Proposals: {len(offers)}")
                
                return True
            else:
                print(f"❌ Email analysis failed: {response.status_code}")
                print(f"   Error: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Email analysis error: {e}")
            return False
    
    async def run_all_tests(self):
        """Run all enhanced feature tests"""
        print("🚀 FULMARK EMAIL INTELLIGENCE - ENHANCED FEATURES TEST")
        print("=" * 60)
        
        tests = [
            ("System Health", self.test_system_health),
            ("Enhanced Email Analysis", self.test_email_analysis),
            ("Auto Kanban Creation", self.test_auto_kanban_creation),
            ("Calendar Structuring", self.test_calendar_structuring),
            ("PDF Proposal Generation", self.test_pdf_proposal_generation)
        ]
        
        results = []
        
        for test_name, test_func in tests:
            try:
                result = await test_func()
                results.append((test_name, result))
            except Exception as e:
                print(f"❌ {test_name} failed with exception: {e}")
                results.append((test_name, False))
        
        # Summary
        print("\n" + "=" * 60)
        print("🎯 TEST RESULTS SUMMARY")
        print("=" * 60)
        
        passed = 0
        for test_name, result in results:
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"{status} - {test_name}")
            if result:
                passed += 1
        
        print(f"\n🏆 Overall: {passed}/{len(results)} tests passed")
        
        if passed == len(results):
            print("🎉 ALL TESTS PASSED! FULMARK Email Intelligence is FANTASTIC!")
        else:
            print("⚠️ Some tests failed. Check the logs above.")
        
        await self.client.aclose()


async def main():
    """Main test function"""
    tester = EnhancedFeaturesTest()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
