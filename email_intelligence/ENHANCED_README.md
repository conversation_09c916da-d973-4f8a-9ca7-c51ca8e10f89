# 🚀 FULMARK Email Intelligence System - ENHANCED

**Najlepszy system Email Intelligence dla branży HVAC w Europie!**

AI-powered email and transcription analysis with automatic Kanban creation, PDF proposals, and calendar intelligence.

## 🌟 Enhanced Features (NEW!)

### 🎯 **Automatic Kanban Card Creation**
- Automatyczne tworzenie kart Kanban z analizy emaili i transkrypcji
- Inteligentne przypisywanie do odpowiednich etapów workflow
- Szacowanie przychodów i złożoności zadań
- Rozpoznawanie polskiej terminologii HVAC

### 📄 **Professional PDF Proposal Generation**
- Automatyczne generowanie profesjonalnych ofert PDF
- Branding Fulmark z logo i kolorystyką
- Inteligentne wyceny na podstawie analizy AI
- Rekomendacje sprzętu (Daikin, LG, Mitsubishi)

### 🗓️ **Calendar Intelligence**
- Strukturyzacja źle zorganizowanych wydarzeń kalendarzowych
- Kategoryzacja na 3 typy: Serwis (czerwony), Nowa Instalacja (niebieski), Oględziny (zielony)
- Optymalizacja tras i harmonogramów dla Warszawy
- Uwzględnienie wzorców ruchu i sezonowości

### 🧠 **Enhanced AI Analysis**
- Rozpoznawanie polskich typów budynków (kamienica, blok, dom)
- Analiza marek sprzętu HVAC popularnych w Polsce
- Sezonowe uwagi dla klimatu warszawskiego
- Inteligentne wykrywanie potrzeb klientów

## 🚀 Quick Start

### 1. Install Dependencies
```bash
# Using pip
pip install -r requirements.txt

# Or using UV (recommended)
uv pip install -r requirements.txt
```

### 2. Configure Environment
Create `.env` file with your configuration:
```bash
# AI Configuration
OPENAI_API_KEY=your_openai_key
OPENAI_MODEL=gpt-4-turbo-preview

# Email Accounts
DOLORES_EMAIL=<EMAIL>
DOLORES_PASSWORD=Blaeritipol1
GRZEGORZ_EMAIL=<EMAIL>
GRZEGORZ_PASSWORD=Blaeritipol1

# External Services
WEAVIATE_URL=http://localhost:8080
REDIS_URL=redis://**************:3037
MONGODB_URL=mongodb://**************:27017
CRM_API_URL=http://localhost:5000/api
```

### 3. Start the System
```bash
# Development mode
python main.py

# Production mode with Docker
docker-compose up -d
```

### 4. Test Enhanced Features
```bash
# Run comprehensive test suite
python test_enhanced_features.py
```

## 🎯 API Endpoints

### Core Endpoints
- `GET /` - System information
- `GET /health` - Health check with all services status
- `POST /analyze` - Enhanced email analysis with AI insights

### 🆕 Enhanced Endpoints
- `POST /auto-kanban` - **Automatically create Kanban cards from emails**
- `POST /generate-proposal` - **Generate professional PDF proposals**
- `POST /structure-calendar` - **Structure poorly organized calendar events**
- `GET /search` - Semantic search with Weaviate
- `GET /insights` - AI-generated business insights

## 🎯 Usage Examples

### Auto Kanban Creation
```python
import httpx

async with httpx.AsyncClient() as client:
    response = await client.post("http://localhost:8001/auto-kanban", params={
        "email_content": "Problem z klimatyzacją Daikin na Mokotowie...",
        "sender": "<EMAIL>",
        "subject": "Naprawa klimatyzacji",
        "force_create": True
    })
    
    result = response.json()
    print(f"Created {result['kanban_cards_created']} Kanban cards!")
```

### PDF Proposal Generation
```python
offer_data = {
    "offer_type": "installation",
    "equipment_recommended": ["Daikin FTXM35R"],
    "service_description": "Instalacja klimatyzacji w mieszkaniu",
    "estimated_price_range": "8000-12000 PLN"
}

customer_data = {
    "name": "Jan Kowalski",
    "email": "<EMAIL>"
}

response = await client.post("http://localhost:8001/generate-proposal", json={
    "offer_data": offer_data,
    "customer_data": customer_data
})
```

### Calendar Structuring
```python
unstructured_events = [
    {
        "title": "klimatyzacja kowalski mokotów",
        "description": "naprawa daikin nie działa",
        "location": "puławska 123"
    }
]

response = await client.post("http://localhost:8001/structure-calendar", 
                           json=unstructured_events)
```

## 🏗️ Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    FULMARK EMAIL INTELLIGENCE               │
├─────────────────────────────────────────────────────────────┤
│ FastAPI Server (main.py)                                   │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│ │ AI Analyzer │ │ CRM         │ │ PDF         │ │Calendar │ │
│ │ (CrewAI)    │ │ Integrator  │ │ Generator   │ │Intel.   │ │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐             │
│ │ Email       │ │ Weaviate    │ │ Redis       │             │
│ │ Processor   │ │ Client      │ │ Cache       │             │
│ └─────────────┘ └─────────────┘ └─────────────┘             │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 Configuration

All configuration is handled in `config.py`. Key settings:

- **Email Accounts**: dolores@ (transcriptions), grzegorz@ (customer emails)
- **AI Models**: OpenAI GPT-4 for analysis, Whisper for transcription
- **HVAC Categories**: Service types, equipment, priorities
- **Polish Market**: Warsaw-specific optimizations

## 🧪 Testing

Run the comprehensive test suite:
```bash
# Test all enhanced features
python test_enhanced_features.py

# Test individual components
python -m pytest tests/
```

## 🎉 Success Metrics

After implementing these enhancements, expect:
- ✅ **90%+ Manager Task Automation**: Automatic Kanban and proposal creation
- ✅ **Professional Image**: Branded PDF proposals
- ✅ **Organized Calendar**: Structured events with optimization
- ✅ **Polish Market Focus**: HVAC terminology and Warsaw specifics
- ✅ **Seamless Integration**: Works with existing HVAC CRM

## 🏆 Why This is FANTASTIC

1. **🎯 Automatic Kanban**: Boss nie musi ręcznie tworzyć zadań!
2. **📄 Professional Proposals**: Automatyczne oferty z brandingiem Fulmark
3. **🗓️ Calendar Intelligence**: Koniec z chaosem w kalendarzu
4. **🇵🇱 Polish Market**: Dostosowane do polskiego rynku HVAC
5. **🚀 European Leadership**: Najbardziej zaawansowany system w Europie

**Status**: 🏆 **READY TO DOMINATE POLISH HVAC MARKET!**

## 📞 Support & Documentation

For detailed documentation and support:
- Check the `/docs` directory for technical specifications
- Run `python test_enhanced_features.py` for feature demonstrations
- Review `config.py` for all configuration options
- Monitor logs for system health and performance

---

**FULMARK Email Intelligence System** - Transforming HVAC business operations through AI! 🚀
